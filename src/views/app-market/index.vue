<template>
  <div class="p-6 space-y-6">
    <!-- 页面标题和统计 -->
    <div class="flex justify-between items-center">
      <div>
        <h1 class="text-2xl font-bold text-gray-900 dark:text-dark-text">应用市场</h1>
        <p class="text-gray-600 dark:text-dark-text-secondary mt-1">发现和管理您的应用工具</p>
      </div>
      <div class="flex items-center space-x-6 text-sm">
        <div class="text-center">
          <div class="text-2xl font-bold text-primary-600">{{ totalApps }}</div>
          <div class="text-gray-500 dark:text-dark-text-secondary">总应用数</div>
        </div>
        <div class="text-center">
          <div class="text-2xl font-bold text-green-600">{{ favoriteCount }}</div>
          <div class="text-gray-500 dark:text-dark-text-secondary">我的收藏</div>
        </div>
        <div class="text-center">
          <div class="text-2xl font-bold text-blue-600">{{ installedCount }}</div>
          <div class="text-gray-500 dark:text-dark-text-secondary">已安装</div>
        </div>
      </div>
    </div>

    <!-- 搜索和筛选区域 -->
    <div class="bg-white dark:bg-dark-surface rounded-xl p-6 shadow-elegant dark:shadow-elegant-dark border border-gray-100 dark:border-dark-border">
      <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between space-y-4 lg:space-y-0 lg:space-x-4">
        <!-- 搜索框 -->
        <div class="flex-1 max-w-md">
          <el-input
            v-model="searchKeyword"
            placeholder="搜索应用名称、描述或标签..."
            @input="handleSearch"
            clearable
          >
            <template #prefix>
              <MagnifyingGlassIcon class="w-4 h-4 text-gray-400" />
            </template>
          </el-input>
        </div>

        <!-- 筛选器 -->
        <div class="flex items-center space-x-4">
          <!-- 分类筛选 -->
          <el-select
            v-model="selectedCategory"
            placeholder="选择分类"
            clearable
            style="width: 140px"
            @change="handleCategoryChange"
          >
            <el-option label="全部分类" value="" />
            <el-option
              v-for="category in categoryOptions"
              :key="category.value"
              :label="category.label"
              :value="category.value"
            />
          </el-select>

          <!-- 价格类型筛选 -->
          <el-select
            v-model="selectedPriceType"
            placeholder="价格类型"
            clearable
            style="width: 120px"
            @change="handlePriceTypeChange"
          >
            <el-option label="全部价格" value="" />
            <el-option
              v-for="priceType in priceTypeOptions"
              :key="priceType.value"
              :label="priceType.label"
              :value="priceType.value"
            />
          </el-select>

          <!-- 评分筛选 -->
          <el-select
            v-model="selectedRating"
            placeholder="最低评分"
            clearable
            style="width: 120px"
            @change="handleRatingChange"
          >
            <el-option label="全部评分" value="" />
            <el-option label="4星以上" :value="4" />
            <el-option label="3星以上" :value="3" />
            <el-option label="2星以上" :value="2" />
          </el-select>

          <!-- 排序 -->
          <el-select
            v-model="sortBy"
            placeholder="排序方式"
            style="width: 140px"
            @change="handleSortChange"
          >
            <el-option label="按名称排序" value="name" />
            <el-option label="按评分排序" value="rating" />
            <el-option label="按下载量排序" value="downloadCount" />
            <el-option label="按更新时间排序" value="lastUpdated" />
            <el-option label="按价格排序" value="price" />
          </el-select>

          <!-- 排序方向 -->
          <el-button
            @click="toggleSortOrder"
            :icon="sortOrder === 'asc' ? ArrowUpIcon : ArrowDownIcon"
            circle
            size="default"
          />

          <!-- 清除筛选 -->
          <el-button @click="clearAllFilters" type="info" plain>
            清除筛选
          </el-button>
        </div>
      </div>
    </div>

    <!-- 标签页 -->
    <div class="bg-white dark:bg-dark-surface rounded-xl shadow-elegant dark:shadow-elegant-dark border border-gray-100 dark:border-dark-border overflow-hidden">
      <el-tabs v-model="activeTab" class="app-market-tabs">
        <el-tab-pane label="全部应用" name="all">
          <AppGrid
            :apps="filteredApps"
            :loading="loading"
            @app-click="handleAppClick"
            @favorite-toggle="handleFavoriteToggle"
            @install-toggle="handleInstallToggle"
            @purchase="handlePurchase"
          />
        </el-tab-pane>
        <el-tab-pane name="favorites">
          <template #label>
            <span class="flex items-center">
              我的收藏
              <el-badge :value="favoriteCount" :hidden="favoriteCount === 0" class="ml-2" />
            </span>
          </template>
          <AppGrid
            :apps="favoriteApps"
            :loading="loading"
            @app-click="handleAppClick"
            @favorite-toggle="handleFavoriteToggle"
            @install-toggle="handleInstallToggle"
            @purchase="handlePurchase"
          />
        </el-tab-pane>
      </el-tabs>
    </div>

    <!-- 应用详情弹窗 -->
    <AppDetailsDialog
      v-model="showDetailsDialog"
      :app="selectedApp"
      @favorite-toggle="handleFavoriteToggle"
      @install-toggle="handleInstallToggle"
    />

    <!-- 购买弹窗 -->
    <PurchaseDialog
      v-model="showPurchaseDialog"
      :app="selectedPurchaseApp"
      @purchase-success="handlePurchaseSuccess"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue';
import { ElMessage } from 'element-plus';
import {
  MagnifyingGlassIcon,
  ArrowUpIcon,
  ArrowDownIcon
} from '@heroicons/vue/24/outline';
import AppGrid from './components/AppGrid.vue';
import AppDetailsDialog from './components/AppDetailsDialog.vue';
import PurchaseDialog from './components/PurchaseDialog.vue';
import {
  initAppMarket,
  getAllApps,
  getFavoriteApps,
  getFilteredApps,
  setFilter,
  clearFilter,
  toggleFavorite,
  toggleInstall,
  getAppById,
  appMarketStore,
  AppCategory,
  PriceType,
  type AppInfo,
  type FilterOptions
} from '../../store/app-market';

// 响应式数据
const activeTab = ref('all');
const searchKeyword = ref('');
const selectedCategory = ref('');
const selectedPriceType = ref('');
const selectedRating = ref<number | ''>('');
const sortBy = ref('name');
const sortOrder = ref<'asc' | 'desc'>('asc');
const showDetailsDialog = ref(false);
const selectedApp = ref<AppInfo | null>(null);
const showPurchaseDialog = ref(false);
const selectedPurchaseApp = ref<AppInfo | null>(null);

// 计算属性
const loading = computed(() => appMarketStore.loading.value);
const totalApps = computed(() => getAllApps().length);
const favoriteCount = computed(() => getFavoriteApps.value.length);
const installedCount = computed(() => appMarketStore.installedApps.value.length);
const filteredApps = computed(() => getFilteredApps.value);
const favoriteApps = computed(() => getFavoriteApps.value);

// 分类选项
const categoryOptions = computed(() => [
  { label: '图像处理', value: AppCategory.IMAGE_PROCESSING },
  { label: '数据分析', value: AppCategory.DATA_ANALYSIS },
  { label: 'SEO工具', value: AppCategory.SEO_TOOLS },
  { label: '市场分析', value: AppCategory.MARKET_ANALYSIS },
  { label: '管理工具', value: AppCategory.MANAGEMENT_TOOLS },
  { label: '自动化工具', value: AppCategory.AUTOMATION },
  { label: '内容创作', value: AppCategory.CONTENT_CREATION }
]);

// 价格类型选项
const priceTypeOptions = computed(() => [
  { label: '免费', value: PriceType.FREE },
  { label: '一口价', value: PriceType.ONE_TIME },
  { label: '包月', value: PriceType.MONTHLY },
  { label: '按次计费', value: PriceType.PER_USE }
]);

// 方法
const handleSearch = () => {
  updateFilter();
};

const handleCategoryChange = () => {
  updateFilter();
};

const handlePriceTypeChange = () => {
  updateFilter();
};

const handleRatingChange = () => {
  updateFilter();
};

const handleSortChange = () => {
  updateFilter();
};

const toggleSortOrder = () => {
  sortOrder.value = sortOrder.value === 'asc' ? 'desc' : 'asc';
  updateFilter();
};

const updateFilter = () => {
  const filter: FilterOptions = {
    searchKeyword: searchKeyword.value || undefined,
    category: selectedCategory.value as AppCategory || undefined,
    priceType: selectedPriceType.value as PriceType || undefined,
    rating: selectedRating.value as number || undefined,
    sortBy: sortBy.value as any,
    sortOrder: sortOrder.value
  };
  setFilter(filter);
};

const clearAllFilters = () => {
  searchKeyword.value = '';
  selectedCategory.value = '';
  selectedPriceType.value = '';
  selectedRating.value = '';
  sortBy.value = 'name';
  sortOrder.value = 'asc';
  clearFilter();
};

const handleAppClick = (app: AppInfo) => {
  selectedApp.value = app;
  showDetailsDialog.value = true;
};

const handleFavoriteToggle = (appId: string) => {
  const isFavorited = toggleFavorite(appId);
  const app = getAppById(appId);
  if (app) {
    ElMessage.success(
      isFavorited ? `已收藏 ${app.name}` : `已取消收藏 ${app.name}`
    );
  }
};

const handleInstallToggle = (appId: string) => {
  const isInstalled = toggleInstall(appId);
  const app = getAppById(appId);
  if (app) {
    ElMessage.success(
      isInstalled ? `已安装 ${app.name}` : `已卸载 ${app.name}`
    );
  }
};

const handlePurchase = (app: AppInfo) => {
  selectedPurchaseApp.value = app;
  showPurchaseDialog.value = true;
};

const handlePurchaseSuccess = (appId: string) => {
  // 购买成功后自动安装应用
  handleInstallToggle(appId);
  showPurchaseDialog.value = false;
};

// 生命周期
onMounted(() => {
  initAppMarket();
});
</script>

<style scoped>
/* 应用市场标签页样式 */
:deep(.app-market-tabs) {
  .el-tabs__header {
    margin: 0;
    border-bottom: 1px solid var(--el-border-color-light);
    background: transparent;
  }

  .el-tabs__nav-wrap {
    padding: 0 24px;
  }

  .el-tabs__item {
    height: 48px;
    line-height: 48px;
    font-weight: 500;
    color: var(--el-text-color-regular);
    border-bottom: 2px solid transparent;
    transition: all 0.3s ease;
  }

  .el-tabs__item:hover {
    color: var(--el-color-primary);
  }

  .el-tabs__item.is-active {
    color: var(--el-color-primary);
    border-bottom-color: var(--el-color-primary);
  }

  .el-tabs__content {
    padding: 24px;
  }
}

/* 暗黑模式适配 */
.dark :deep(.app-market-tabs) {
  .el-tabs__header {
    border-bottom-color: var(--el-border-color-dark);
  }

  .el-tabs__item {
    color: var(--el-text-color-regular);
  }

  .el-tabs__item:hover,
  .el-tabs__item.is-active {
    color: var(--el-color-primary);
  }
}

/* 优雅阴影效果 */
.shadow-elegant {
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

.dark .shadow-elegant-dark {
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.3), 0 2px 4px -1px rgba(0, 0, 0, 0.2);
}
</style>
