<template>
  <div class="app-card group relative bg-white dark:bg-dark-surface rounded-xl border border-gray-200 dark:border-dark-border hover:border-primary-300 dark:hover:border-primary-600 transition-all duration-300 hover:shadow-lg dark:hover:shadow-xl cursor-pointer overflow-hidden"
       @click="$emit('click', app)">
    
    <!-- 收藏按钮 -->
    <button
      @click.stop="$emit('favorite-toggle', app.id)"
      class="absolute top-3 right-3 z-10 p-2 rounded-full bg-white/80 dark:bg-dark-card/80 backdrop-blur-sm border border-gray-200 dark:border-dark-border hover:bg-white dark:hover:bg-dark-card transition-all duration-200 group/fav"
      :class="app.isFavorited ? 'text-red-500 hover:text-red-600' : 'text-gray-400 hover:text-red-500'"
    >
      <HeartIcon 
        class="w-4 h-4 transition-transform duration-200 group-hover/fav:scale-110"
        :class="app.isFavorited ? 'fill-current' : ''"
      />
    </button>

    <!-- 应用图标和状态 -->
    <div class="relative p-6 pb-4">
      <div class="flex items-start space-x-4">
        <!-- 应用图标 -->
        <div class="flex-shrink-0">
          <div class="w-16 h-16 bg-gradient-to-br from-primary-100 to-primary-200 dark:from-primary-900/30 dark:to-primary-800/30 rounded-xl flex items-center justify-center text-2xl font-bold border border-primary-200 dark:border-primary-700">
            {{ app.icon }}
          </div>
        </div>

        <!-- 应用信息 -->
        <div class="flex-1 min-w-0">
          <div class="flex items-center space-x-2 mb-1">
            <h3 class="text-lg font-semibold text-gray-900 dark:text-dark-text truncate">
              {{ app.name }}
            </h3>
            <!-- 状态标识 -->
            <el-tag
              v-if="app.status === 'maintenance'"
              type="warning"
              size="small"
              effect="plain"
            >
              维护中
            </el-tag>
            <el-tag
              v-else-if="app.status === 'deprecated'"
              type="danger"
              size="small"
              effect="plain"
            >
              已废弃
            </el-tag>
          </div>
          
          <!-- 开发者 -->
          <p class="text-sm text-gray-500 dark:text-dark-text-secondary mb-2">
            {{ app.developer }}
          </p>
          
          <!-- 评分和下载量 -->
          <div class="flex items-center space-x-4 text-sm">
            <div class="flex items-center space-x-1">
              <div class="flex items-center">
                <StarIcon
                  v-for="i in 5"
                  :key="i"
                  class="w-4 h-4"
                  :class="i <= Math.floor(app.rating) ? 'text-yellow-400 fill-current' : 'text-gray-300 dark:text-gray-600'"
                />
              </div>
              <span class="text-gray-600 dark:text-dark-text-secondary ml-1">
                {{ app.rating }}
              </span>
            </div>
            <div class="text-gray-500 dark:text-dark-text-secondary">
              {{ formatDownloadCount(app.downloadCount) }} 次使用
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 应用描述 -->
    <div class="px-6 pb-4">
      <p class="text-sm text-gray-600 dark:text-dark-text-secondary line-clamp-2 leading-relaxed">
        {{ app.description }}
      </p>
    </div>

    <!-- 标签 -->
    <div class="px-6 pb-4">
      <div class="flex flex-wrap gap-1">
        <el-tag
          v-for="tag in app.tags.slice(0, 3)"
          :key="tag"
          size="small"
          effect="plain"
          class="text-xs"
        >
          {{ tag }}
        </el-tag>
        <span
          v-if="app.tags.length > 3"
          class="text-xs text-gray-400 dark:text-gray-500 px-2 py-1"
        >
          +{{ app.tags.length - 3 }}
        </span>
      </div>
    </div>

    <!-- 价格和操作区域 -->
    <div class="px-6 py-4 bg-gray-50 dark:bg-dark-card/50 border-t border-gray-100 dark:border-dark-border">
      <div class="flex items-center justify-between">
        <!-- 价格信息 -->
        <div class="flex-1">
          <PriceDisplay :price="app.price" size="small" />
        </div>

        <!-- 操作按钮 -->
        <div class="flex items-center space-x-2">
          <!-- 购买/安装按钮 -->
          <el-button
            v-if="!app.isInstalled"
            @click.stop="$emit('purchase', app)"
            type="primary"
            size="small"
            class="min-w-[60px]"
          >
            {{ getButtonText() }}
          </el-button>
          <el-button
            v-else
            @click.stop="$emit('install-toggle', app.id)"
            type="success"
            plain
            size="small"
            class="min-w-[60px]"
          >
            已安装
          </el-button>
        </div>
      </div>
    </div>

    <!-- 悬停效果遮罩 -->
    <div class="absolute inset-0 bg-gradient-to-t from-primary-500/5 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300 pointer-events-none rounded-xl"></div>
  </div>
</template>

<script setup lang="ts">
import { HeartIcon, StarIcon } from '@heroicons/vue/24/outline';
import PriceDisplay from './PriceDisplay.vue';
import type { AppInfo } from '../../../store/app-market';

// Props
const props = defineProps<{
  app: AppInfo;
}>();

// Emits
defineEmits<{
  'click': [app: AppInfo];
  'favorite-toggle': [appId: string];
  'install-toggle': [appId: string];
  'purchase': [app: AppInfo];
}>();

// 获取按钮文本
const getButtonText = () => {
  switch (props.app.price.type) {
    case 'free':
      return '安装';
    case 'one_time':
      return '购买';
    case 'monthly':
      return '订阅';
    case 'per_use':
      return '充值';
    default:
      return '安装';
  }
};

// 格式化下载量
const formatDownloadCount = (count: number): string => {
  if (count >= 10000) {
    return `${(count / 10000).toFixed(1)}万`;
  } else if (count >= 1000) {
    return `${(count / 1000).toFixed(1)}k`;
  }
  return count.toString();
};
</script>

<style scoped>
.app-card {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.app-card:hover {
  transform: translateY(-2px);
}

/* 文本截断样式 */
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* 星级评分动画 */
.star-rating {
  transition: all 0.2s ease;
}

/* 悬停时的微妙动画 */
.group:hover .group-hover\:scale-110 {
  transform: scale(1.1);
}

/* 价格区域样式优化 */
.price-section {
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.05) 0%, rgba(147, 51, 234, 0.05) 100%);
}

.dark .price-section {
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.1) 0%, rgba(147, 51, 234, 0.1) 100%);
}
</style>
