<template>
  <div class="p-6 bg-gray-50 dark:bg-dark-background min-h-screen">
    <!-- 页面头部 -->
    <div class="flex justify-between items-center mb-6">
      <div>
        <h1 class="text-2xl font-bold text-gray-900 dark:text-dark-text mb-2">工作流</h1>
        <p class="text-gray-600 dark:text-dark-text-secondary">查看工作流执行历史和管理工作流</p>
      </div>
      <div class="flex space-x-3">
        <el-button
          @click="showCreateDialog = true"
          type="primary"
          size="large"
          :icon="Plus"
        >
          新建工作流
        </el-button>
        <el-button
          @click="ElMessage.info('工作流模板功能开发中...')"
          size="large"
        >
          工作流模板
        </el-button>
        <el-button
          @click="exportWorkflows"
          size="large"
          :icon="Download"
        >
          导出
        </el-button>
      </div>
    </div>

    <!-- 搜索和筛选区域 -->
    <div class="bg-white dark:bg-dark-surface rounded-xl shadow-elegant dark:shadow-elegant-dark border border-gray-100 dark:border-dark-border p-4 mb-6">
      <div class="flex justify-between items-center">
        <div class="flex space-x-4">
          <el-input
            v-model="searchKeyword"
            placeholder="搜索工作流名称..."
            style="width: 300px"
            clearable
            @input="handleSearch"
          >
            <template #prefix>
              <MagnifyingGlassIcon class="w-4 h-4 text-gray-400" />
            </template>
          </el-input>

          <el-select v-model="statusFilter" placeholder="状态筛选" style="width: 120px" clearable @change="handleSearch">
            <el-option label="全部" value="" />
            <el-option label="启用" value="enabled" />
            <el-option label="禁用" value="disabled" />
          </el-select>
        </div>

        <div class="flex items-center space-x-4">
          <div class="text-sm text-gray-600 dark:text-dark-text-secondary">
            共 {{ pagination.total }} 条执行记录
          </div>
          <div v-if="selectedRows.length > 0" class="flex items-center space-x-2">
            <span class="text-sm text-blue-600 dark:text-blue-400">已选择 {{ selectedRows.length }} 项</span>
            <el-button
              @click="batchToggleStatus"
              type="primary"
              size="small"
              plain
            >
              批量启用/禁用
            </el-button>
          </div>
        </div>
      </div>
    </div>

    <!-- 工作流执行历史列表 -->
    <div class="bg-white dark:bg-dark-surface rounded-xl shadow-elegant dark:shadow-elegant-dark border border-gray-100 dark:border-dark-border overflow-hidden">
      <el-table
        :data="currentPageExecutions"
        v-loading="loading"
        @selection-change="handleSelectionChange"
        class="w-full"
        :header-cell-style="{ backgroundColor: '#f8fafc', color: '#374151', fontWeight: '600' }"
      >
        <el-table-column type="selection" width="55" />
        <el-table-column prop="id" label="执行ID" width="120" />
        <el-table-column prop="workflowName" label="工作流名称" min-width="200" />
        <el-table-column label="工作流程" min-width="300">
          <template #default="{ row }">
            <div class="flex items-center space-x-2">
              <div class="flex items-center bg-gray-50 dark:bg-dark-card rounded-lg px-3 py-2 space-x-2 min-w-0 flex-1">
                <!-- 开始节点 -->
                <div class="flex items-center space-x-1 flex-shrink-0">
                  <div class="w-6 h-6 bg-green-500 rounded-full flex items-center justify-center">
                    <PlayIcon class="w-3 h-3 text-white" />
                  </div>
                  <span class="text-xs text-gray-600 dark:text-dark-text-secondary">开始</span>
                </div>

                <!-- 箭头 -->
                <ArrowRightIcon class="w-4 h-4 text-gray-400 flex-shrink-0" />

                <!-- 应用节点 -->
                <div class="flex items-center space-x-1 overflow-x-auto">
                  <div
                    v-for="(app, index) in row.workflow.apps"
                    :key="index"
                    class="flex items-center space-x-1 flex-shrink-0"
                  >
                    <div
                      class="flex items-center space-x-1 rounded px-2 py-1"
                      :class="getAppStatusClass(row.stepResults[index]?.status)"
                    >
                      <component :is="getAppIcon(app.type)" class="w-4 h-4" />
                      <span class="text-xs">{{ app.name }}</span>
                    </div>
                    <ArrowRightIcon v-if="index < row.workflow.apps.length - 1" class="w-3 h-3 text-gray-400" />
                  </div>
                </div>

                <!-- 箭头 -->
                <ArrowRightIcon class="w-4 h-4 text-gray-400 flex-shrink-0" />

                <!-- 结束节点 -->
                <div class="flex items-center space-x-1 flex-shrink-0">
                  <div class="w-6 h-6 bg-red-500 rounded-full flex items-center justify-center">
                    <StopIcon class="w-3 h-3 text-white" />
                  </div>
                  <span class="text-xs text-gray-600 dark:text-dark-text-secondary">结束</span>
                </div>
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="执行状态" width="120" align="center">
          <template #default="{ row }">
            <el-tag
              :type="getStatusType(row.status)"
              size="small"
            >
              {{ getStatusText(row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="duration" label="执行时长" width="120" align="center" />
        <el-table-column prop="executor" label="执行人" width="120" />
        <el-table-column prop="startTime" label="开始时间" width="180" />
        <el-table-column label="操作" width="150" align="center">
          <template #default="{ row }">
            <div class="flex justify-center space-x-1">
              <el-button
                @click="viewExecutionDetails(row)"
                type="primary"
                size="small"
                plain
              >
                查看详情
              </el-button>
              <el-button
                @click="viewResults(row)"
                type="success"
                size="small"
                plain
                :disabled="row.status !== 'completed'"
              >
                处理结果
              </el-button>
            </div>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="px-6 py-4 border-t border-gray-100 dark:border-dark-border">
        <el-pagination
          v-model:current-page="pagination.currentPage"
          v-model:page-size="pagination.pageSize"
          :total="pagination.total"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="loadWorkflows"
          @current-change="loadWorkflows"
        />
      </div>
    </div>

    <!-- 创建工作流弹窗 -->
    <CreateWorkflowDialog v-model="showCreateDialog" @success="refreshData" />

    <!-- 工作流模板弹窗 -->
    <!-- <WorkflowTemplateDialog v-model="showTemplateDialog" @select="createFromTemplate" /> -->

    <!-- 执行详情弹窗 -->
    <!-- <ExecutionDetailsDialog v-model="showDetailsDialog" :execution="selectedExecution" /> -->

    <!-- 处理结果弹窗 -->
    <!-- <ResultsDialog v-model="showResultsDialog" :execution="selectedExecution" /> -->
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue';
import { ElMessage } from 'element-plus';
import { Plus, Download } from '@element-plus/icons-vue';
import {
  MagnifyingGlassIcon,
  ArrowRightIcon,
  PlayIcon,
  StopIcon
} from '@heroicons/vue/24/outline';
import {
  ShoppingBagIcon,
  ScissorsIcon,
  SparklesIcon,
  DocumentTextIcon,
  RectangleStackIcon,
  CubeIcon
} from '@heroicons/vue/24/outline';
import CreateWorkflowDialog from './components/CreateWorkflowDialog.vue';
// import WorkflowTemplateDialog from './components/WorkflowTemplateDialog.vue';
// import ExecutionDetailsDialog from './components/ExecutionDetailsDialog.vue';
// import ResultsDialog from './components/ResultsDialog.vue';
import {
  initWorkflows,
  getWorkflows,
  getWorkflowExecutions,
  type Workflow,
  type WorkflowExecution
} from '../../store/workflows';

// 响应式数据
const searchKeyword = ref('');
const statusFilter = ref('');
const selectedRows = ref<WorkflowExecution[]>([]);
const showCreateDialog = ref(false);
const showTemplateDialog = ref(false);
const showDetailsDialog = ref(false);
const showResultsDialog = ref(false);
const selectedExecution = ref<WorkflowExecution | null>(null);

const pagination = ref({
  currentPage: 1,
  pageSize: 20,
  total: 0
});

// 使用存储中的数据
const executions = ref<WorkflowExecution[]>([]);
const loading = ref(false);

// 计算属性
const filteredExecutions = computed(() => {
  let filtered = executions.value;

  // 搜索过滤
  if (searchKeyword.value) {
    filtered = filtered.filter(execution =>
      execution.workflowName.toLowerCase().includes(searchKeyword.value.toLowerCase()) ||
      execution.id.toLowerCase().includes(searchKeyword.value.toLowerCase())
    );
  }

  // 状态过滤
  if (statusFilter.value) {
    filtered = filtered.filter(execution => execution.status === statusFilter.value);
  }

  return filtered;
});

const currentPageExecutions = computed(() => {
  const start = (pagination.value.currentPage - 1) * pagination.value.pageSize;
  const end = start + pagination.value.pageSize;
  return filteredExecutions.value.slice(start, end);
});

// 获取应用图标
const getAppIcon = (appType: string) => {
  const iconMap: Record<string, any> = {
    'product-collection': ShoppingBagIcon,
    'smart-crop': ScissorsIcon,
    'one-click-cutout': SparklesIcon,
    'super-split': CubeIcon,
    'title-generator': DocumentTextIcon,
    'batch-listing': RectangleStackIcon,
    'pod-compose': CubeIcon
  };
  return iconMap[appType] || CubeIcon;
};

// 获取应用状态样式
const getAppStatusClass = (status: string) => {
  switch (status) {
    case 'completed':
      return 'bg-green-100 dark:bg-green-900/30 text-green-700 dark:text-green-300';
    case 'failed':
      return 'bg-red-100 dark:bg-red-900/30 text-red-700 dark:text-red-300';
    case 'running':
      return 'bg-blue-100 dark:bg-blue-900/30 text-blue-700 dark:text-blue-300';
    case 'pending':
      return 'bg-gray-100 dark:bg-gray-900/30 text-gray-700 dark:text-gray-300';
    default:
      return 'bg-gray-100 dark:bg-gray-900/30 text-gray-700 dark:text-gray-300';
  }
};

// 获取状态类型
const getStatusType = (status: string) => {
  switch (status) {
    case 'completed':
      return 'success';
    case 'failed':
      return 'danger';
    case 'running':
      return 'warning';
    case 'pending':
      return 'info';
    default:
      return 'info';
  }
};

// 获取状态文本
const getStatusText = (status: string) => {
  switch (status) {
    case 'completed':
      return '已完成';
    case 'failed':
      return '失败';
    case 'running':
      return '执行中';
    case 'pending':
      return '等待中';
    default:
      return '未知';
  }
};

// 方法
const loadExecutions = async () => {
  loading.value = true;
  try {
    const data = await getWorkflowExecutions();
    executions.value = data;
    pagination.value.total = filteredExecutions.value.length;
  } catch (error) {
    ElMessage.error('加载执行历史失败');
  } finally {
    loading.value = false;
  }
};

const handleSelectionChange = (selection: WorkflowExecution[]) => {
  selectedRows.value = selection;
};

const handleSearch = () => {
  pagination.value.currentPage = 1;
  pagination.value.total = filteredExecutions.value.length;
};

const viewExecutionDetails = (execution: WorkflowExecution) => {
  selectedExecution.value = execution;
  ElMessage.info('执行详情功能开发中...');
  // showDetailsDialog.value = true;
};

const viewResults = (execution: WorkflowExecution) => {
  selectedExecution.value = execution;
  ElMessage.info('处理结果功能开发中...');
  // showResultsDialog.value = true;
};

const createFromTemplate = (template: Workflow) => {
  // 从模板创建工作流的逻辑
  ElMessage.success(`正在从模板"${template.name}"创建工作流...`);
  showCreateDialog.value = true;
};

const exportWorkflows = () => {
  ElMessage.success('导出执行历史功能开发中...');
};

const refreshData = () => {
  ElMessage.success('操作成功！');
  loadExecutions();
};

// 生命周期
onMounted(() => {
  initWorkflows();
  loadExecutions();
});
</script>
