import{c as n,a as e,o,r as z,f as W,d as re,h as U,j as d,k as _,m as R,l as s,A as V,n as Z,t as l,B as T,F as P,s as te,z as A,L as ie,x as ue,_ as ce,e as Se,K as De,p as de,w as je,M as ze,N as Me,O as Ee,E as H,G as Ie,H as Ne,I as We,C as Be,b as Le,D as Ae}from"./index-g0Lcbgij.js";import{r as be}from"./MagnifyingGlassIcon-B6kQhNbF.js";function ae(M,S){return o(),n("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M13.5 4.5 21 12m0 0-7.5 7.5M21 12H3"})])}function pe(M,S){return o(),n("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M19.5 14.25v-2.625a3.375 3.375 0 0 0-3.375-3.375h-1.5A1.125 1.125 0 0 1 13.5 7.125v-1.5a3.375 3.375 0 0 0-3.375-3.375H8.25m0 12.75h7.5m-7.5 3H12M10.5 2.25H5.625c-.621 0-1.125.504-1.125 1.125v17.25c0 .621.504 1.125 1.125 1.125h12.75c.621 0 1.125-.504 1.125-1.125V11.25a9 9 0 0 0-9-9Z"})])}function ye(M,S){return o(),n("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M5.25 5.653c0-.856.917-1.398 1.667-.986l11.54 6.347a1.125 1.125 0 0 1 0 1.972l-11.54 6.347a1.125 1.125 0 0 1-1.667-.986V5.653Z"})])}function me(M,S){return o(),n("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"m7.848 8.25 1.536.887M7.848 8.25a3 3 0 1 1-5.196-3 3 3 0 0 1 5.196 3Zm1.536.887a2.165 2.165 0 0 1 1.083 1.839c.005.351.054.695.14 1.024M9.384 9.137l2.077 1.199M7.848 15.75l1.536-.887m-1.536.887a3 3 0 1 1-5.196 3 3 3 0 0 1 5.196-3Zm1.536-.887a2.165 2.165 0 0 0 1.083-1.838c.005-.352.054-.695.14-1.025m-1.223 2.863 2.077-1.199m0-3.328a4.323 4.323 0 0 1 2.068-1.379l5.325-1.628a4.5 4.5 0 0 1 2.48-.044l.803.215-7.794 4.5m-2.882-1.664A4.33 4.33 0 0 0 10.607 12m3.736 0 7.794 4.5-.802.215a4.5 4.5 0 0 1-2.48-.043l-5.326-1.629a4.324 4.324 0 0 1-2.068-1.379M14.343 12l-2.882 1.664"})])}function ge(M,S){return o(),n("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M9.813 15.904 9 18.75l-.813-2.846a4.5 4.5 0 0 0-3.09-3.09L2.25 12l2.846-.813a4.5 4.5 0 0 0 3.09-3.09L9 5.25l.813 2.846a4.5 4.5 0 0 0 3.09 3.09L15.75 12l-2.846.813a4.5 4.5 0 0 0-3.09 3.09ZM18.259 8.715 18 9.75l-.259-1.035a3.375 3.375 0 0 0-2.455-2.456L14.25 6l1.036-.259a3.375 3.375 0 0 0 2.455-2.456L18 2.25l.259 1.035a3.375 3.375 0 0 0 2.456 2.456L21.75 6l-1.035.259a3.375 3.375 0 0 0-2.456 2.456ZM16.894 20.567 16.5 21.75l-.394-1.183a2.25 2.25 0 0 0-1.423-1.423L13.5 18.75l1.183-.394a2.25 2.25 0 0 0 1.423-1.423l.394-1.183.394 1.183a2.25 2.25 0 0 0 1.423 1.423l1.183.394-1.183.394a2.25 2.25 0 0 0-1.423 1.423Z"})])}function _e(M,S){return o(),n("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M5.25 7.5A2.25 2.25 0 0 1 7.5 5.25h9a2.25 2.25 0 0 1 2.25 2.25v9a2.25 2.25 0 0 1-2.25 2.25h-9a2.25 2.25 0 0 1-2.25-2.25v-9Z"})])}const se=z([]),fe=z([]),oe=z(!1),ee=[{id:"WF001",name:"商品采集+智能裁图+批量刊登",apps:[{id:"app1",name:"商品采集",type:"product-collection",settings:{mode:"auto",productSelection:"manual",timeout:30,onError:"stop"}},{id:"app2",name:"智能裁图",type:"smart-crop",settings:{mode:"auto",productSelection:"previous",timeout:20,onError:"skip"}},{id:"app3",name:"批量刊登",type:"batch-listing",settings:{mode:"manual",productSelection:"previous",timeout:60,onError:"stop"}}],usageCount:156,status:"enabled",creator:"张三",createTime:"2024-01-15 10:30:00"},{id:"WF002",name:"一键抠图+超级裂变",apps:[{id:"app1",name:"一键抠图",type:"one-click-cutout",settings:{mode:"auto",productSelection:"manual",timeout:25,onError:"retry"}},{id:"app2",name:"超级裂变",type:"super-split",settings:{mode:"auto",productSelection:"previous",timeout:30,onError:"stop"}}],usageCount:89,status:"enabled",creator:"李四",createTime:"2024-01-14 14:20:00"},{id:"WF003",name:"标题生成+POD合成+批量刊登",apps:[{id:"app1",name:"标题生成",type:"title-generator",settings:{mode:"manual",productSelection:"manual",timeout:15,onError:"stop"}},{id:"app2",name:"POD合成",type:"pod-compose",settings:{mode:"auto",productSelection:"previous",timeout:45,onError:"retry"}},{id:"app3",name:"批量刊登",type:"batch-listing",settings:{mode:"manual",productSelection:"previous",timeout:60,onError:"stop"}}],usageCount:234,status:"disabled",creator:"王五",createTime:"2024-01-13 09:15:00"},{id:"WF004",name:"智能裁图+一键抠图",apps:[{id:"app1",name:"智能裁图",type:"smart-crop",settings:{mode:"auto",productSelection:"manual",timeout:20,onError:"skip"}},{id:"app2",name:"一键抠图",type:"one-click-cutout",settings:{mode:"auto",productSelection:"previous",timeout:25,onError:"retry"}}],usageCount:67,status:"enabled",creator:"赵六",createTime:"2024-01-12 16:45:00"},{id:"WF005",name:"完整电商流程",apps:[{id:"app1",name:"商品采集",type:"product-collection",settings:{mode:"auto",productSelection:"manual",timeout:30,onError:"stop"}},{id:"app2",name:"智能裁图",type:"smart-crop",settings:{mode:"auto",productSelection:"previous",timeout:20,onError:"skip"}},{id:"app3",name:"一键抠图",type:"one-click-cutout",settings:{mode:"auto",productSelection:"previous",timeout:25,onError:"retry"}},{id:"app4",name:"标题生成",type:"title-generator",settings:{mode:"manual",productSelection:"previous",timeout:15,onError:"stop"}},{id:"app5",name:"批量刊登",type:"batch-listing",settings:{mode:"manual",productSelection:"previous",timeout:60,onError:"stop"}}],usageCount:423,status:"enabled",creator:"孙七",createTime:"2024-01-11 11:20:00"}],Re=()=>{se.value=[...ee]},Ue=()=>se.value,Pe=async M=>{oe.value=!0;try{await new Promise(N=>setTimeout(N,1e3));const S={id:`WF${String(se.value.length+1).padStart(3,"0")}`,name:M.name,apps:M.apps,usageCount:0,status:"enabled",creator:"当前用户",createTime:new Date().toLocaleString("zh-CN",{year:"numeric",month:"2-digit",day:"2-digit",hour:"2-digit",minute:"2-digit",second:"2-digit"})};return se.value.unshift(S),S}finally{oe.value=!1}},Fe=[{id:"EXE001",workflowId:"WF001",workflowName:"商品采集+智能裁图+批量刊登",workflow:ee[0],status:"completed",stepResults:[{appId:"app1",appName:"商品采集",status:"completed",startTime:"2024-01-15 10:30:00",endTime:"2024-01-15 10:35:00",duration:"5分钟",inputCount:0,outputCount:50},{appId:"app2",appName:"智能裁图",status:"completed",startTime:"2024-01-15 10:35:00",endTime:"2024-01-15 10:40:00",duration:"5分钟",inputCount:50,outputCount:50},{appId:"app3",appName:"批量刊登",status:"completed",startTime:"2024-01-15 10:40:00",endTime:"2024-01-15 10:45:00",duration:"5分钟",inputCount:50,outputCount:48}],startTime:"2024-01-15 10:30:00",endTime:"2024-01-15 10:45:00",duration:"15分钟",executor:"张三"},{id:"EXE002",workflowId:"WF002",workflowName:"一键抠图+超级裂变",workflow:ee[1],status:"running",stepResults:[{appId:"app1",appName:"一键抠图",status:"completed",startTime:"2024-01-15 14:20:00",endTime:"2024-01-15 14:25:00",duration:"5分钟",inputCount:30,outputCount:30},{appId:"app2",appName:"超级裂变",status:"running",startTime:"2024-01-15 14:25:00",inputCount:30,outputCount:0}],startTime:"2024-01-15 14:20:00",duration:"8分钟",executor:"李四"},{id:"EXE003",workflowId:"WF001",workflowName:"商品采集+智能裁图+批量刊登",workflow:ee[0],status:"failed",stepResults:[{appId:"app1",appName:"商品采集",status:"completed",startTime:"2024-01-15 09:00:00",endTime:"2024-01-15 09:05:00",duration:"5分钟",inputCount:0,outputCount:25},{appId:"app2",appName:"智能裁图",status:"failed",startTime:"2024-01-15 09:05:00",endTime:"2024-01-15 09:07:00",duration:"2分钟",inputCount:25,outputCount:0,errorMessage:"图片格式不支持"}],startTime:"2024-01-15 09:00:00",endTime:"2024-01-15 09:07:00",duration:"7分钟",executor:"王五"},{id:"EXE004",workflowId:"WF005",workflowName:"完整电商流程",workflow:ee[4],status:"completed",stepResults:[{appId:"app1",appName:"商品采集",status:"completed",startTime:"2024-01-14 16:00:00",endTime:"2024-01-14 16:10:00",duration:"10分钟",inputCount:0,outputCount:100},{appId:"app2",appName:"智能裁图",status:"completed",startTime:"2024-01-14 16:10:00",endTime:"2024-01-14 16:20:00",duration:"10分钟",inputCount:100,outputCount:100},{appId:"app3",appName:"一键抠图",status:"completed",startTime:"2024-01-14 16:20:00",endTime:"2024-01-14 16:30:00",duration:"10分钟",inputCount:100,outputCount:95},{appId:"app4",appName:"标题生成",status:"completed",startTime:"2024-01-14 16:30:00",endTime:"2024-01-14 16:35:00",duration:"5分钟",inputCount:95,outputCount:95},{appId:"app5",appName:"批量刊登",status:"completed",startTime:"2024-01-14 16:35:00",endTime:"2024-01-14 16:45:00",duration:"10分钟",inputCount:95,outputCount:92}],startTime:"2024-01-14 16:00:00",endTime:"2024-01-14 16:45:00",duration:"45分钟",executor:"孙七"},{id:"EXE005",workflowId:"WF003",workflowName:"标题生成+POD合成+批量刊登",workflow:ee[2],status:"pending",stepResults:[{appId:"app1",appName:"标题生成",status:"pending"},{appId:"app2",appName:"POD合成",status:"pending"},{appId:"app3",appName:"批量刊登",status:"pending"}],startTime:"2024-01-15 15:00:00",executor:"赵六"}],Oe=async()=>{oe.value=!0;try{return await new Promise(M=>setTimeout(M,500)),fe.value=[...Fe],fe.value}finally{oe.value=!1}};W(()=>se.value),W(()=>fe.value),W(()=>oe.value);const Ze={class:"mb-4"},He={class:"grid grid-cols-1 md:grid-cols-2 gap-6 max-h-[500px] overflow-y-auto"},Xe=["onClick"],Ke={class:"flex justify-between items-start mb-3"},qe={class:"text-lg font-semibold text-gray-900 dark:text-dark-text"},Je={class:"text-sm text-gray-600 dark:text-dark-text-secondary mt-1"},Ge={class:"mb-3"},Qe={class:"flex items-center space-x-2 overflow-x-auto pb-2"},Ye={class:"flex items-center space-x-1 flex-shrink-0"},et={class:"w-6 h-6 bg-green-500 rounded-full flex items-center justify-center"},tt={class:"flex items-center space-x-1 bg-blue-100 dark:bg-blue-900/30 rounded px-2 py-1 flex-shrink-0"},at={class:"text-xs text-blue-700 dark:text-blue-300"},st={class:"flex items-center space-x-1 flex-shrink-0"},ot={class:"w-6 h-6 bg-red-500 rounded-full flex items-center justify-center"},rt={class:"flex justify-between items-center text-sm text-gray-500 dark:text-dark-text-secondary"},lt={key:0,class:"text-center py-12"},nt={class:"flex justify-end space-x-3"},dt=re({__name:"WorkflowTemplateDialog",props:{modelValue:{type:Boolean}},emits:["update:modelValue","select","create-blank"],setup(M,{emit:S}){const N=M,B=S,j=W({get:()=>N.modelValue,set:I=>B("update:modelValue",I)}),D=z(""),$=W(()=>Ue()),g=W(()=>D.value?$.value.filter(I=>I.name.toLowerCase().includes(D.value.toLowerCase())||I.apps.some(w=>w.name.toLowerCase().includes(D.value.toLowerCase()))):$.value),E=I=>({"product-collection":ue,"smart-crop":me,"one-click-cutout":ge,"super-split":A,"title-generator":pe,"batch-listing":ie,"pod-compose":A})[I]||A,y=I=>{B("select",I),L()},c=()=>{B("create-blank"),L()},L=()=>{D.value="",j.value=!1};return(I,w)=>{const p=_("el-input"),i=_("el-tag"),v=_("el-button"),r=_("el-dialog");return o(),U(r,{modelValue:j.value,"onUpdate:modelValue":w[1]||(w[1]=k=>j.value=k),title:"工作流模板",width:"1000px","close-on-click-modal":!1,class:"template-dialog"},{footer:d(()=>[e("div",nt,[s(v,{onClick:L,size:"large"},{default:d(()=>w[5]||(w[5]=[T(" 取消 ")])),_:1,__:[5]}),s(v,{onClick:c,type:"primary",size:"large",plain:""},{default:d(()=>w[6]||(w[6]=[T(" 创建空白工作流 ")])),_:1,__:[6]})])]),default:d(()=>[e("div",Ze,[s(p,{modelValue:D.value,"onUpdate:modelValue":w[0]||(w[0]=k=>D.value=k),placeholder:"搜索模板...",size:"large",clearable:""},{prefix:d(()=>[s(V(be),{class:"w-5 h-5 text-gray-400"})]),_:1},8,["modelValue"])]),e("div",He,[(o(!0),n(P,null,Z(g.value,k=>(o(),n("div",{key:k.id,class:"bg-gray-50 dark:bg-dark-card rounded-lg p-4 border border-gray-200 dark:border-dark-border hover:border-blue-300 dark:hover:border-blue-600 cursor-pointer transition-all duration-200",onClick:m=>y(k)},[e("div",Ke,[e("div",null,[e("h3",qe,l(k.name),1),e("p",Je,l(k.apps.length)+" 个应用 · 使用 "+l(k.usageCount)+" 次 ",1)]),s(i,{type:k.status==="enabled"?"success":"danger",size:"small"},{default:d(()=>[T(l(k.status==="enabled"?"可用":"禁用"),1)]),_:2},1032,["type"])]),e("div",Ge,[e("div",Qe,[e("div",Ye,[e("div",et,[s(V(ye),{class:"w-3 h-3 text-white"})]),w[2]||(w[2]=e("span",{class:"text-xs text-gray-600 dark:text-dark-text-secondary"},"开始",-1))]),(o(!0),n(P,null,Z(k.apps,(m,F)=>(o(),n(P,{key:F},[s(V(ae),{class:"w-3 h-3 text-gray-400 flex-shrink-0"}),e("div",tt,[(o(),U(te(E(m.type)),{class:"w-3 h-3 text-blue-600 dark:text-blue-400"})),e("span",at,l(m.name),1)])],64))),128)),s(V(ae),{class:"w-3 h-3 text-gray-400 flex-shrink-0"}),e("div",st,[e("div",ot,[s(V(_e),{class:"w-3 h-3 text-white"})]),w[3]||(w[3]=e("span",{class:"text-xs text-gray-600 dark:text-dark-text-secondary"},"结束",-1))])])]),e("div",rt,[e("span",null,"创建者："+l(k.creator),1),e("span",null,l(k.createTime),1)])],8,Xe))),128))]),g.value.length===0?(o(),n("div",lt,[s(V(A),{class:"w-16 h-16 mx-auto text-gray-400 mb-4"}),w[4]||(w[4]=e("p",{class:"text-gray-500 dark:text-dark-text-secondary"},"没有找到匹配的模板",-1))])):R("",!0)]),_:1},8,["modelValue"])}}}),he=ce(dt,[["__scopeId","data-v-56b174c9"]]),it={class:"flex h-[600px]"},ut={class:"w-64 border-r border-gray-200 dark:border-dark-border pr-4"},ct={class:"mb-4"},pt={class:"flex justify-between items-center mb-2"},mt={class:"space-y-2 max-h-[500px] overflow-y-auto"},gt=["onDragstart"],xt={class:"flex-1 min-w-0"},kt={class:"text-sm font-medium text-gray-900 dark:text-dark-text truncate"},vt={class:"text-xs text-gray-500 dark:text-dark-text-secondary truncate"},ft={class:"flex-1 px-4"},bt={class:"mb-4"},yt={class:"flex flex-col items-center space-y-6"},_t={class:"flex flex-col items-center space-y-2"},wt={class:"w-16 h-16 bg-green-500 rounded-full flex items-center justify-center shadow-lg"},ht={key:0,class:"flex flex-col items-center space-y-6 w-full"},$t={class:"w-full max-w-2xl"},Ct=["onClick"],Vt={class:"flex flex-col items-center space-y-2 p-3"},Tt={class:"w-14 h-14 bg-blue-500 rounded-full flex items-center justify-center relative shadow-md"},St=["onClick"],Dt={class:"text-xs text-gray-600 dark:text-dark-text-secondary font-medium text-center max-w-20 truncate"},jt={class:"flex flex-col items-center space-y-2"},zt={class:"w-16 h-16 bg-red-500 rounded-full flex items-center justify-center shadow-lg"},Mt={key:0,class:"absolute inset-0 flex items-center justify-center"},Et={class:"text-center text-gray-500 dark:text-dark-text-secondary"},It={class:"w-80 border-l border-gray-200 dark:border-dark-border pl-4"},Nt={key:0,class:"space-y-4"},Wt={class:"bg-gray-50 dark:bg-dark-card rounded-lg p-4"},Bt={class:"flex items-center space-x-3 mb-4"},Lt={class:"font-medium text-gray-900 dark:text-dark-text"},At={class:"text-sm text-gray-500 dark:text-dark-text-secondary"},Rt={class:"space-y-4"},Ut={key:1,class:"text-center text-gray-500 dark:text-dark-text-secondary py-8"},Pt={class:"flex justify-end space-x-3"},Ft=re({__name:"CreateWorkflowDialog",props:{modelValue:{type:Boolean}},emits:["update:modelValue","success"],setup(M,{emit:S}){const N=M,B=S,j=W({get:()=>N.modelValue,set:x=>B("update:modelValue",x)}),D=z(!1),$=z(""),g=z(null),E=z(!1),y=Se({name:""}),c=z([]),L=[{id:"product-collection",name:"商品采集",type:"product-collection",description:"采集电商平台商品信息"},{id:"smart-crop",name:"智能裁图",type:"smart-crop",description:"智能裁剪商品图片"},{id:"one-click-cutout",name:"一键抠图",type:"one-click-cutout",description:"自动抠图去背景"},{id:"super-split",name:"超级裂变",type:"super-split",description:"图片批量裂变处理"},{id:"title-generator",name:"标题生成",type:"title-generator",description:"智能生成商品标题"},{id:"batch-listing",name:"批量刊登",type:"batch-listing",description:"批量刊登商品到平台"},{id:"pod-compose",name:"POD合成",type:"pod-compose",description:"POD商品合成处理"}],I=W(()=>L.filter(x=>x.name.toLowerCase().includes($.value.toLowerCase())||x.description.toLowerCase().includes($.value.toLowerCase()))),w=W(()=>y.name.trim()&&c.value.length>0),p=x=>({"product-collection":ue,"smart-crop":me,"one-click-cutout":ge,"super-split":A,"title-generator":pe,"batch-listing":ie,"pod-compose":A})[x]||A,i=()=>{H.info("跳转到应用市场功能开发中...")},v=x=>{y.name=`${x.name} - 副本`,c.value=x.apps.map(t=>{const O=L.find(Y=>Y.type===t.type);return{id:`app_${Date.now()}_${Math.random().toString(36).substring(2,9)}`,name:t.name,type:t.type,description:(O==null?void 0:O.description)||"",settings:{...t.settings},datasetConfig:{}}}),E.value=!1,H.success(`已从模板"${x.name}"加载配置`)},r=(x,t)=>{x.dataTransfer&&(x.dataTransfer.setData("application/json",JSON.stringify(t)),x.dataTransfer.effectAllowed="copy")},k=x=>{x.preventDefault(),x.dataTransfer&&(x.dataTransfer.dropEffect="copy")},m=x=>{if(x.preventDefault(),x.dataTransfer)try{const t=JSON.parse(x.dataTransfer.getData("application/json"));F(t)}catch(t){console.error("Failed to parse dropped data:",t)}},F=x=>{const t={...x,id:`${x.id}_${Date.now()}`,settings:{mode:"auto",productSelection:"previous",timeout:30,onError:"stop"},datasetConfig:{}};c.value.push(t),g.value=c.value.length-1},Q=x=>{c.value.splice(x,1),g.value===x?g.value=null:g.value!==null&&g.value>x&&g.value--},q=()=>{g.value=null},C=async()=>{if(w.value){D.value=!0;try{await Pe({name:y.name,apps:c.value}),H.success("工作流创建成功！"),B("success"),h()}catch{H.error("创建失败，请重试")}finally{D.value=!1}}},h=()=>{y.name="",c.value=[],g.value=null,$.value="",E.value=!1,j.value=!1};return(x,t)=>{const O=_("el-button"),Y=_("el-input"),le=_("el-radio"),xe=_("el-radio-group"),J=_("el-option"),u=_("el-select"),a=_("el-input-number"),G=_("el-dialog");return o(),U(G,{modelValue:j.value,"onUpdate:modelValue":t[10]||(t[10]=f=>j.value=f),title:"新建工作流",width:"1200px","close-on-click-modal":!1,"close-on-press-escape":!1,class:"workflow-dialog"},{footer:d(()=>[e("div",Pt,[s(O,{onClick:h,size:"large"},{default:d(()=>t[28]||(t[28]=[T("取消")])),_:1,__:[28]}),s(O,{onClick:C,type:"primary",size:"large",loading:D.value,disabled:!w.value},{default:d(()=>[T(l(D.value?"创建中...":"确定创建"),1)]),_:1},8,["loading","disabled"])])]),default:d(()=>[e("div",it,[e("div",ut,[e("div",ct,[e("div",pt,[t[13]||(t[13]=e("h3",{class:"text-lg font-semibold text-gray-900 dark:text-dark-text"},"可用应用",-1)),s(O,{onClick:i,type:"primary",size:"small",plain:""},{default:d(()=>t[11]||(t[11]=[T(" 应用市场 ")])),_:1,__:[11]}),s(O,{onClick:t[0]||(t[0]=f=>E.value=!0),size:"small",plain:""},{default:d(()=>t[12]||(t[12]=[T(" 选择模板 ")])),_:1,__:[12]})]),s(Y,{modelValue:$.value,"onUpdate:modelValue":t[1]||(t[1]=f=>$.value=f),placeholder:"搜索应用...",size:"small",clearable:""},{prefix:d(()=>[s(V(be),{class:"w-4 h-4 text-gray-400"})]),_:1},8,["modelValue"])]),e("div",mt,[(o(!0),n(P,null,Z(I.value,f=>(o(),n("div",{key:f.id,class:"flex items-center p-3 bg-gray-50 dark:bg-dark-card rounded-lg cursor-pointer hover:bg-gray-100 dark:hover:bg-dark-card/80 transition-colors duration-200",draggable:"true",onDragstart:X=>r(X,f)},[(o(),U(te(p(f.type)),{class:"w-5 h-5 text-blue-600 dark:text-blue-400 mr-3"})),e("div",xt,[e("div",kt,l(f.name),1),e("div",vt,l(f.description),1)]),s(V(De),{class:"w-4 h-4 text-gray-400"})],40,gt))),128))])]),e("div",ft,[e("div",bt,[t[14]||(t[14]=e("h3",{class:"text-lg font-semibold text-gray-900 dark:text-dark-text mb-2"},"工作流设计",-1)),s(Y,{modelValue:y.name,"onUpdate:modelValue":t[2]||(t[2]=f=>y.name=f),placeholder:"请输入工作流名称...",size:"small",class:"mb-2"},null,8,["modelValue"])]),e("div",{class:"bg-white dark:bg-dark-surface rounded-lg p-6 min-h-[450px] border border-gray-200 dark:border-dark-border relative",onDrop:m,onDragover:k},[e("div",yt,[e("div",_t,[e("div",wt,[s(V(ye),{class:"w-8 h-8 text-white"})]),t[15]||(t[15]=e("span",{class:"text-sm font-medium text-gray-700 dark:text-dark-text"},"开始",-1))]),c.value.length>0?(o(),n("div",ht,[t[16]||(t[16]=e("div",{class:"w-px h-8 bg-gray-300 dark:bg-gray-600"},null,-1)),e("div",$t,[s(V(Me),{modelValue:c.value,"onUpdate:modelValue":t[3]||(t[3]=f=>c.value=f),onEnd:q,"item-key":"id",animation:200,handle:".drag-handle","ghost-class":"sortable-ghost","chosen-class":"sortable-chosen","drag-class":"sortable-drag",class:"grid grid-cols-4 gap-4 justify-items-center"},{item:d(({element:f,index:X})=>[e("div",{class:de(["relative group cursor-pointer drag-handle",{"ring-2 ring-blue-500 rounded-lg":g.value===X}]),onClick:ke=>g.value=X},[e("div",Vt,[e("div",Tt,[(o(),U(te(p(f.type)),{class:"w-7 h-7 text-white"})),e("button",{onClick:je(ke=>Q(X),["stop"]),class:"absolute -top-2 -right-2 w-6 h-6 bg-red-500 rounded-full flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity duration-200 shadow-md"},[s(V(ze),{class:"w-3 h-3 text-white"})],8,St)]),e("span",Dt,l(f.name),1)])],10,Ct)]),_:1},8,["modelValue"])]),t[17]||(t[17]=e("div",{class:"w-px h-8 bg-gray-300 dark:bg-gray-600"},null,-1))])):R("",!0),e("div",jt,[e("div",zt,[s(V(_e),{class:"w-8 h-8 text-white"})]),t[18]||(t[18]=e("span",{class:"text-sm font-medium text-gray-700 dark:text-dark-text"},"结束",-1))])]),c.value.length===0?(o(),n("div",Mt,[e("div",Et,[s(V(A),{class:"w-12 h-12 mx-auto mb-2 opacity-50"}),t[19]||(t[19]=e("p",{class:"text-sm"},"拖拽左侧应用到此处开始构建工作流",-1))])])):R("",!0)],32)]),e("div",It,[t[27]||(t[27]=e("div",{class:"mb-4"},[e("h3",{class:"text-lg font-semibold text-gray-900 dark:text-dark-text"},"应用设置")],-1)),g.value!==null&&c.value[g.value]?(o(),n("div",Nt,[e("div",Wt,[e("div",Bt,[(o(),U(te(p(c.value[g.value].type)),{class:"w-6 h-6 text-blue-600 dark:text-blue-400"})),e("div",null,[e("div",Lt,l(c.value[g.value].name),1),e("div",At,l(c.value[g.value].description),1)])]),e("div",Rt,[e("div",null,[t[22]||(t[22]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-dark-text mb-2"},"执行模式",-1)),s(xe,{modelValue:c.value[g.value].settings.mode,"onUpdate:modelValue":t[4]||(t[4]=f=>c.value[g.value].settings.mode=f),size:"small"},{default:d(()=>[s(le,{label:"auto"},{default:d(()=>t[20]||(t[20]=[T("自动执行")])),_:1,__:[20]}),s(le,{label:"manual"},{default:d(()=>t[21]||(t[21]=[T("手动确认")])),_:1,__:[21]})]),_:1},8,["modelValue"])]),e("div",null,[t[23]||(t[23]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-dark-text mb-2"},"产品选择",-1)),s(u,{modelValue:c.value[g.value].settings.productSelection,"onUpdate:modelValue":t[5]||(t[5]=f=>c.value[g.value].settings.productSelection=f),placeholder:"选择产品来源",size:"small",class:"w-full"},{default:d(()=>[s(J,{label:"使用上一步结果",value:"previous"}),s(J,{label:"手动选择",value:"manual"}),s(J,{label:"全部产品",value:"all"})]),_:1},8,["modelValue"])]),e("div",null,[t[24]||(t[24]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-dark-text mb-1"},"超时时间（分钟）",-1)),s(a,{modelValue:c.value[g.value].settings.timeout,"onUpdate:modelValue":t[6]||(t[6]=f=>c.value[g.value].settings.timeout=f),min:1,max:60,size:"small",class:"w-full"},null,8,["modelValue"])]),e("div",null,[t[25]||(t[25]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-dark-text mb-1"},"失败处理",-1)),s(u,{modelValue:c.value[g.value].settings.onError,"onUpdate:modelValue":t[7]||(t[7]=f=>c.value[g.value].settings.onError=f),size:"small",class:"w-full"},{default:d(()=>[s(J,{label:"停止工作流",value:"stop"}),s(J,{label:"跳过继续",value:"skip"}),s(J,{label:"重试",value:"retry"})]),_:1},8,["modelValue"])])])])])):(o(),n("div",Ut,[s(V(Ee),{class:"w-12 h-12 mx-auto mb-2 opacity-50"}),t[26]||(t[26]=e("p",{class:"text-sm"},"选择一个应用节点进行设置",-1))]))])]),s(he,{modelValue:E.value,"onUpdate:modelValue":t[8]||(t[8]=f=>E.value=f),onSelect:v,onCreateBlank:t[9]||(t[9]=f=>E.value=!1)},null,8,["modelValue"])]),_:1},8,["modelValue"])}}}),Ot=ce(Ft,[["__scopeId","data-v-7f899664"]]),Zt={key:0,class:"space-y-6"},Ht={class:"bg-gray-50 dark:bg-dark-card rounded-lg p-4"},Xt={class:"grid grid-cols-2 gap-4"},Kt={class:"text-sm text-gray-900 dark:text-dark-text"},qt={class:"text-sm text-gray-900 dark:text-dark-text"},Jt={class:"text-sm text-gray-900 dark:text-dark-text"},Gt={class:"text-sm text-gray-900 dark:text-dark-text"},Qt={class:"text-sm text-gray-900 dark:text-dark-text"},Yt={class:"bg-white dark:bg-dark-surface rounded-lg border border-gray-200 dark:border-dark-border"},ea={class:"p-4"},ta={class:"space-y-4"},aa={class:"flex flex-col items-center"},sa={class:"flex-1 min-w-0"},oa={class:"flex items-center justify-between mb-2"},ra={class:"flex items-center space-x-2"},la={class:"text-base font-medium text-gray-900 dark:text-dark-text"},na={class:"grid grid-cols-2 gap-4 text-sm"},da={key:0},ia={class:"text-gray-900 dark:text-dark-text"},ua={key:1},ca={class:"text-gray-900 dark:text-dark-text"},pa={key:2},ma={class:"text-gray-900 dark:text-dark-text"},ga={key:3},xa={class:"text-gray-900 dark:text-dark-text"},ka={key:0,class:"mt-2 p-2 bg-red-50 dark:bg-red-900/20 rounded border border-red-200 dark:border-red-800"},va={class:"text-sm text-red-700 dark:text-red-300"},fa={class:"flex justify-end"},ba=re({__name:"ExecutionDetailsDialog",props:{modelValue:{type:Boolean},execution:{}},emits:["update:modelValue"],setup(M,{emit:S}){const N=M,B=S,j=W({get:()=>N.modelValue,set:p=>B("update:modelValue",p)}),D=p=>({"product-collection":ue,"smart-crop":me,"one-click-cutout":ge,"super-split":A,"title-generator":pe,"batch-listing":ie,"pod-compose":A})[p]||A,$=p=>({商品采集:"product-collection",智能裁图:"smart-crop",一键抠图:"one-click-cutout",超级裂变:"super-split",标题生成:"title-generator",批量刊登:"batch-listing",POD合成:"pod-compose"})[p]||"unknown",g=p=>{switch(p){case"completed":return"success";case"failed":return"danger";case"running":return"warning";case"pending":return"info";default:return"info"}},E=p=>{switch(p){case"completed":return"已完成";case"failed":return"失败";case"running":return"执行中";case"pending":return"等待中";default:return"未知"}},y=p=>g(p),c=p=>E(p),L=p=>{switch(p){case"completed":return"bg-green-500 text-white";case"failed":return"bg-red-500 text-white";case"running":return"bg-blue-500 text-white";case"pending":return"bg-gray-300 dark:bg-gray-600 text-gray-700 dark:text-gray-300";default:return"bg-gray-300 dark:bg-gray-600 text-gray-700 dark:text-gray-300"}},I=p=>{switch(p){case"completed":return"bg-green-300 dark:bg-green-600";case"failed":return"bg-red-300 dark:bg-red-600";case"running":return"bg-blue-300 dark:bg-blue-600";default:return"bg-gray-300 dark:bg-gray-600"}},w=()=>{j.value=!1};return(p,i)=>{const v=_("el-tag"),r=_("el-button"),k=_("el-dialog");return o(),U(k,{modelValue:j.value,"onUpdate:modelValue":i[0]||(i[0]=m=>j.value=m),title:"执行详情",width:"800px","close-on-click-modal":!1,class:"execution-dialog"},{footer:d(()=>[e("div",fa,[s(r,{onClick:w,size:"large"},{default:d(()=>i[14]||(i[14]=[T("关闭")])),_:1,__:[14]})])]),default:d(()=>[p.execution?(o(),n("div",Zt,[e("div",Ht,[i[7]||(i[7]=e("h3",{class:"text-lg font-semibold text-gray-900 dark:text-dark-text mb-3"},"基本信息",-1)),e("div",Xt,[e("div",null,[i[1]||(i[1]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-dark-text mb-1"},"执行ID",-1)),e("p",Kt,l(p.execution.id),1)]),e("div",null,[i[2]||(i[2]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-dark-text mb-1"},"工作流名称",-1)),e("p",qt,l(p.execution.workflowName),1)]),e("div",null,[i[3]||(i[3]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-dark-text mb-1"},"执行状态",-1)),s(v,{type:g(p.execution.status),size:"small"},{default:d(()=>[T(l(E(p.execution.status)),1)]),_:1},8,["type"])]),e("div",null,[i[4]||(i[4]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-dark-text mb-1"},"执行人",-1)),e("p",Jt,l(p.execution.executor),1)]),e("div",null,[i[5]||(i[5]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-dark-text mb-1"},"开始时间",-1)),e("p",Gt,l(p.execution.startTime),1)]),e("div",null,[i[6]||(i[6]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-dark-text mb-1"},"执行时长",-1)),e("p",Qt,l(p.execution.duration||"进行中"),1)])])]),e("div",Yt,[i[13]||(i[13]=e("h3",{class:"text-lg font-semibold text-gray-900 dark:text-dark-text p-4 border-b border-gray-200 dark:border-dark-border"}," 执行步骤 ",-1)),e("div",ea,[e("div",ta,[(o(!0),n(P,null,Z(p.execution.stepResults,(m,F)=>(o(),n("div",{key:m.appId,class:"flex items-start space-x-4"},[e("div",aa,[e("div",{class:de(["w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium",L(m.status)])},l(F+1),3),F<p.execution.stepResults.length-1?(o(),n("div",{key:0,class:de(["w-px h-12 mt-2",I(m.status)])},null,2)):R("",!0)]),e("div",sa,[e("div",oa,[e("div",ra,[(o(),U(te(D($(m.appName))),{class:"w-5 h-5 text-blue-600 dark:text-blue-400"})),e("h4",la,l(m.appName),1),s(v,{type:y(m.status),size:"small"},{default:d(()=>[T(l(c(m.status)),1)]),_:2},1032,["type"])])]),e("div",na,[m.startTime?(o(),n("div",da,[i[8]||(i[8]=e("span",{class:"text-gray-600 dark:text-dark-text-secondary"},"开始时间：",-1)),e("span",ia,l(m.startTime),1)])):R("",!0),m.duration?(o(),n("div",ua,[i[9]||(i[9]=e("span",{class:"text-gray-600 dark:text-dark-text-secondary"},"执行时长：",-1)),e("span",ca,l(m.duration),1)])):R("",!0),m.inputCount!==void 0?(o(),n("div",pa,[i[10]||(i[10]=e("span",{class:"text-gray-600 dark:text-dark-text-secondary"},"输入数量：",-1)),e("span",ma,l(m.inputCount),1)])):R("",!0),m.outputCount!==void 0?(o(),n("div",ga,[i[11]||(i[11]=e("span",{class:"text-gray-600 dark:text-dark-text-secondary"},"输出数量：",-1)),e("span",xa,l(m.outputCount),1)])):R("",!0)]),m.errorMessage?(o(),n("div",ka,[e("p",va,[i[12]||(i[12]=e("strong",null,"错误信息：",-1)),T(l(m.errorMessage),1)])])):R("",!0)])]))),128))])])])])):R("",!0)]),_:1},8,["modelValue"])}}}),ya=ce(ba,[["__scopeId","data-v-5b52e5e1"]]),_a={key:0,class:"space-y-6"},wa={class:"bg-gray-50 dark:bg-dark-card rounded-lg p-4"},ha={class:"grid grid-cols-4 gap-4"},$a={class:"text-center"},Ca={class:"text-2xl font-bold text-blue-600 dark:text-blue-400"},Va={class:"text-center"},Ta={class:"text-2xl font-bold text-green-600 dark:text-green-400"},Sa={class:"text-center"},Da={class:"text-2xl font-bold text-orange-600 dark:text-orange-400"},ja={class:"text-center"},za={class:"text-2xl font-bold text-purple-600 dark:text-purple-400"},Ma={class:"bg-white dark:bg-dark-surface rounded-lg border border-gray-200 dark:border-dark-border"},Ea={class:"p-4"},Ia={class:"space-y-4"},Na={class:"grid grid-cols-3 gap-4"},Wa={class:"bg-blue-50 dark:bg-blue-900/20 rounded-lg p-3 text-center"},Ba={class:"text-lg font-semibold text-blue-600 dark:text-blue-400"},La={class:"bg-green-50 dark:bg-green-900/20 rounded-lg p-3 text-center"},Aa={class:"text-lg font-semibold text-green-600 dark:text-green-400"},Ra={class:"bg-orange-50 dark:bg-orange-900/20 rounded-lg p-3 text-center"},Ua={class:"text-lg font-semibold text-orange-600 dark:text-orange-400"},Pa={class:"bg-gray-50 dark:bg-dark-card rounded-lg p-4"},Fa={key:0},Oa={class:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4"},Za=["src","alt"],Ha={class:"text-sm font-medium text-gray-900 dark:text-dark-text truncate"},Xa={class:"text-sm text-green-600 dark:text-green-400 font-semibold"},Ka={key:1},qa={class:"grid grid-cols-2 md:grid-cols-4 gap-4"},Ja=["src","alt"],Ga={class:"text-xs text-gray-600 dark:text-dark-text-secondary text-center"},Qa={key:2},Ya={class:"space-y-2"},es={class:"text-sm text-gray-900 dark:text-dark-text"},ts={class:"text-xs text-gray-500 dark:text-dark-text-secondary mt-1"},as={key:3},ss={class:"space-y-2"},os={class:"text-sm font-medium text-gray-900 dark:text-dark-text"},rs={class:"text-xs text-gray-500 dark:text-dark-text-secondary"},ls={key:4},ns={class:"flex justify-between"},ds=re({__name:"ResultsDialog",props:{modelValue:{type:Boolean},execution:{}},emits:["update:modelValue"],setup(M,{emit:S}){const N=M,B=S,j=W({get:()=>N.modelValue,set:v=>B("update:modelValue",v)}),D=z(""),$=W(()=>(N.execution&&N.execution.stepResults.length>0&&(D.value=N.execution.stepResults[0].appId),N.execution)),g=()=>{if(!$.value)return 0;const v=$.value.stepResults[0];return(v==null?void 0:v.inputCount)||0},E=()=>{if(!$.value)return 0;const v=$.value.stepResults[$.value.stepResults.length-1];return(v==null?void 0:v.outputCount)||0},y=()=>{const v=g(),r=E();return v===0?0:Math.round(r/v*100)},c=v=>Array.from({length:Math.min(v,6)},(r,k)=>({id:`product_${k+1}`,title:`商品标题 ${k+1} - 高质量产品描述`,price:(Math.random()*100+10).toFixed(2),image:`https://picsum.photos/200/200?random=${k+1}`})),L=v=>Array.from({length:Math.min(v,8)},(r,k)=>({id:`image_${k+1}`,name:`处理后图片_${k+1}.jpg`,image:`https://picsum.photos/150/150?random=${k+10}`})),I=v=>{const r=["高品质时尚T恤 - 舒适透气 多色可选 男女通用款式","精美陶瓷马克杯 - 创意设计 办公室必备 礼品首选","多功能手机壳 - 防摔保护 时尚外观 适配多机型","舒适运动鞋 - 轻便透气 专业运动 日常休闲两用","优质帆布包 - 大容量设计 环保材质 时尚百搭"];return Array.from({length:Math.min(v,5)},(k,m)=>({id:`title_${m+1}`,title:r[m%r.length],length:r[m%r.length].length}))},w=v=>{const r=["Amazon","eBay","Shopify","Etsy"];return Array.from({length:Math.min(v,8)},(k,m)=>({id:`listing_${m+1}`,platform:r[m%r.length],productId:`PRD${String(m+1).padStart(6,"0")}`,status:Math.random()>.2?"success":"failed"}))},p=()=>{H.success("结果下载功能开发中...")},i=()=>{j.value=!1};return(v,r)=>{const k=_("el-tag"),m=_("el-tab-pane"),F=_("el-tabs"),Q=_("el-button"),q=_("el-dialog");return o(),U(q,{modelValue:j.value,"onUpdate:modelValue":r[1]||(r[1]=C=>j.value=C),title:"处理结果",width:"1000px","close-on-click-modal":!1,class:"results-dialog"},{footer:d(()=>[e("div",ns,[s(Q,{onClick:p,type:"primary",plain:""},{default:d(()=>r[13]||(r[13]=[T(" 下载结果 ")])),_:1,__:[13]}),s(Q,{onClick:i,size:"large"},{default:d(()=>r[14]||(r[14]=[T("关闭")])),_:1,__:[14]})])]),default:d(()=>[$.value?(o(),n("div",_a,[e("div",wa,[r[6]||(r[6]=e("h3",{class:"text-lg font-semibold text-gray-900 dark:text-dark-text mb-3"},"处理概览",-1)),e("div",ha,[e("div",$a,[e("div",Ca,l(g()),1),r[2]||(r[2]=e("div",{class:"text-sm text-gray-600 dark:text-dark-text-secondary"},"总输入",-1))]),e("div",Va,[e("div",Ta,l(E()),1),r[3]||(r[3]=e("div",{class:"text-sm text-gray-600 dark:text-dark-text-secondary"},"总输出",-1))]),e("div",Sa,[e("div",Da,l(y())+"%",1),r[4]||(r[4]=e("div",{class:"text-sm text-gray-600 dark:text-dark-text-secondary"},"成功率",-1))]),e("div",ja,[e("div",za,l($.value.duration||"0分钟"),1),r[5]||(r[5]=e("div",{class:"text-sm text-gray-600 dark:text-dark-text-secondary"},"总耗时",-1))])])]),e("div",Ma,[r[12]||(r[12]=e("div",{class:"p-4 border-b border-gray-200 dark:border-dark-border"},[e("h3",{class:"text-lg font-semibold text-gray-900 dark:text-dark-text"},"各步骤处理结果")],-1)),e("div",Ea,[s(F,{modelValue:D.value,"onUpdate:modelValue":r[0]||(r[0]=C=>D.value=C),type:"border-card"},{default:d(()=>[(o(!0),n(P,null,Z($.value.stepResults,C=>(o(),U(m,{key:C.appId,label:C.appName,name:C.appId},{default:d(()=>[e("div",Ia,[e("div",Na,[e("div",Wa,[e("div",Ba,l(C.inputCount||0),1),r[7]||(r[7]=e("div",{class:"text-sm text-gray-600 dark:text-dark-text-secondary"},"输入数量",-1))]),e("div",La,[e("div",Aa,l(C.outputCount||0),1),r[8]||(r[8]=e("div",{class:"text-sm text-gray-600 dark:text-dark-text-secondary"},"输出数量",-1))]),e("div",Ra,[e("div",Ua,l(C.duration||"0分钟"),1),r[9]||(r[9]=e("div",{class:"text-sm text-gray-600 dark:text-dark-text-secondary"},"处理时长",-1))])]),e("div",Pa,[r[11]||(r[11]=e("h4",{class:"text-base font-medium text-gray-900 dark:text-dark-text mb-3"},"处理结果数据",-1)),C.appName==="商品采集"?(o(),n("div",Fa,[e("div",Oa,[(o(!0),n(P,null,Z(c(C.outputCount||0),h=>(o(),n("div",{key:h.id,class:"bg-white dark:bg-dark-surface rounded-lg p-3 border border-gray-200 dark:border-dark-border"},[e("img",{src:h.image,alt:h.title,class:"w-full h-32 object-cover rounded mb-2"},null,8,Za),e("h5",Ha,l(h.title),1),e("p",Xa,"$"+l(h.price),1)]))),128))])])):C.appName==="智能裁图"||C.appName==="一键抠图"?(o(),n("div",Ka,[e("div",qa,[(o(!0),n(P,null,Z(L(C.outputCount||0),h=>(o(),n("div",{key:h.id,class:"bg-white dark:bg-dark-surface rounded-lg p-2 border border-gray-200 dark:border-dark-border"},[e("img",{src:h.image,alt:h.name,class:"w-full h-24 object-cover rounded mb-1"},null,8,Ja),e("p",Ga,l(h.name),1)]))),128))])])):C.appName==="标题生成"?(o(),n("div",Qa,[e("div",Ya,[(o(!0),n(P,null,Z(I(C.outputCount||0),h=>(o(),n("div",{key:h.id,class:"bg-white dark:bg-dark-surface rounded-lg p-3 border border-gray-200 dark:border-dark-border"},[e("p",es,l(h.title),1),e("p",ts,"长度: "+l(h.length)+" 字符",1)]))),128))])])):C.appName==="批量刊登"?(o(),n("div",as,[e("div",ss,[(o(!0),n(P,null,Z(w(C.outputCount||0),h=>(o(),n("div",{key:h.id,class:"bg-white dark:bg-dark-surface rounded-lg p-3 border border-gray-200 dark:border-dark-border flex justify-between items-center"},[e("div",null,[e("p",os,l(h.platform),1),e("p",rs,l(h.productId),1)]),s(k,{type:h.status==="success"?"success":"danger",size:"small"},{default:d(()=>[T(l(h.status==="success"?"成功":"失败"),1)]),_:2},1032,["type"])]))),128))])])):(o(),n("div",ls,r[10]||(r[10]=[e("p",{class:"text-gray-500 dark:text-dark-text-secondary text-center py-4"}," 暂无结果数据展示 ",-1)])))])])]),_:2},1032,["label","name"]))),128))]),_:1},8,["modelValue"])])])])):R("",!0)]),_:1},8,["modelValue"])}}}),is=ce(ds,[["__scopeId","data-v-392a9730"]]),us={class:"space-y-6"},cs={class:"bg-gradient-to-r from-primary-50 to-blue-50 dark:from-primary-900/20 dark:to-blue-900/20 rounded-2xl p-6 border border-primary-100 dark:border-primary-800"},ps={class:"flex items-center justify-between"},ms={class:"flex space-x-3"},gs={class:"grid grid-cols-1 md:grid-cols-4 gap-6"},xs={class:"bg-white dark:bg-dark-surface rounded-xl p-6 shadow-elegant dark:shadow-elegant-dark border border-gray-100 dark:border-dark-border hover:shadow-lg dark:hover:shadow-2xl transition-all duration-300"},ks={class:"flex items-center justify-between"},vs={class:"text-2xl font-bold text-gray-900 dark:text-dark-text"},fs={class:"bg-white dark:bg-dark-surface rounded-xl p-6 shadow-elegant dark:shadow-elegant-dark border border-gray-100 dark:border-dark-border hover:shadow-lg dark:hover:shadow-2xl transition-all duration-300"},bs={class:"flex items-center justify-between"},ys={class:"text-2xl font-bold text-green-600 dark:text-green-400"},_s={class:"bg-white dark:bg-dark-surface rounded-xl p-6 shadow-elegant dark:shadow-elegant-dark border border-gray-100 dark:border-dark-border hover:shadow-lg dark:hover:shadow-2xl transition-all duration-300"},ws={class:"flex items-center justify-between"},hs={class:"text-2xl font-bold text-purple-600 dark:text-purple-400"},$s={class:"bg-white dark:bg-dark-surface rounded-xl p-6 shadow-elegant dark:shadow-elegant-dark border border-gray-100 dark:border-dark-border hover:shadow-lg dark:hover:shadow-2xl transition-all duration-300"},Cs={class:"flex items-center justify-between"},Vs={class:"text-2xl font-bold text-orange-600 dark:text-orange-400"},Ts={class:"bg-white dark:bg-dark-surface rounded-xl shadow-elegant dark:shadow-elegant-dark border border-gray-100 dark:border-dark-border p-6"},Ss={class:"flex justify-between items-center"},Ds={class:"flex space-x-4"},js={class:"flex items-center space-x-4"},zs={class:"text-sm text-gray-600 dark:text-dark-text-secondary"},Ms={key:0,class:"flex items-center space-x-2"},Es={class:"text-sm text-blue-600 dark:text-blue-400"},Is={class:"bg-white dark:bg-dark-surface rounded-xl shadow-elegant dark:shadow-elegant-dark border border-gray-100 dark:border-dark-border overflow-hidden"},Ns={class:"flex items-center space-x-2"},Ws={class:"flex items-center bg-gray-50 dark:bg-dark-card rounded-lg px-3 py-2 space-x-2 min-w-0 flex-1"},Bs={class:"flex items-center space-x-1 flex-shrink-0"},Ls={class:"w-6 h-6 bg-green-500 rounded-full flex items-center justify-center"},As={class:"flex items-center space-x-1 overflow-x-auto"},Rs={class:"text-xs"},Us={class:"flex items-center space-x-1 flex-shrink-0"},Ps={class:"w-6 h-6 bg-red-500 rounded-full flex items-center justify-center"},Fs={class:"flex justify-center space-x-1"},Os={class:"px-6 py-4 border-t border-gray-100 dark:border-dark-border"},Xs=re({__name:"index",setup(M){const S=z(""),N=z(""),B=z([]),j=z(!1),D=z(!1),$=z(!1),g=z(!1),E=z(null),y=z({currentPage:1,pageSize:20,total:0}),c=z([]),L=z(!1),I=W(()=>{let u=c.value;return S.value&&(u=u.filter(a=>a.workflowName.toLowerCase().includes(S.value.toLowerCase())||a.id.toLowerCase().includes(S.value.toLowerCase()))),N.value&&(u=u.filter(a=>a.status===N.value)),u}),w=W(()=>{const u=(y.value.currentPage-1)*y.value.pageSize,a=u+y.value.pageSize;return I.value.slice(u,a)}),p=W(()=>c.value.length),i=W(()=>{if(c.value.length===0)return 0;const u=c.value.filter(a=>a.status==="completed").length;return Math.round(u/c.value.length*100)}),v=W(()=>new Set(c.value.map(a=>a.workflowId)).size),r=W(()=>c.value.filter(a=>a.status==="completed"&&a.duration).length===0?"0分钟":"3.5分钟"),k=u=>({"product-collection":ue,"smart-crop":me,"one-click-cutout":ge,"super-split":A,"title-generator":pe,"batch-listing":ie,"pod-compose":A})[u]||A,m=u=>{switch(u){case"completed":return"bg-green-100 dark:bg-green-900/30 text-green-700 dark:text-green-300";case"failed":return"bg-red-100 dark:bg-red-900/30 text-red-700 dark:text-red-300";case"running":return"bg-blue-100 dark:bg-blue-900/30 text-blue-700 dark:text-blue-300";case"pending":return"bg-gray-100 dark:bg-gray-900/30 text-gray-700 dark:text-gray-300";default:return"bg-gray-100 dark:bg-gray-900/30 text-gray-700 dark:text-gray-300"}},F=u=>{switch(u){case"completed":return"success";case"failed":return"danger";case"running":return"warning";case"pending":return"info";default:return"info"}},Q=u=>{switch(u){case"completed":return"已完成";case"failed":return"失败";case"running":return"执行中";case"pending":return"等待中";default:return"未知"}},q=async()=>{L.value=!0;try{const u=await Oe();c.value=u,y.value.total=I.value.length}catch{H.error("加载执行历史失败")}finally{L.value=!1}},C=u=>{B.value=u},h=()=>{y.value.currentPage=1,y.value.total=I.value.length},x=u=>{E.value=u,$.value=!0},t=u=>{E.value=u,g.value=!0},O=u=>{H.success(`正在从模板"${u.name}"创建工作流...`),D.value=!1,j.value=!0},Y=()=>{H.success("导出执行历史功能开发中...")},le=()=>{H.success("操作成功！"),q()},xe=u=>{y.value.pageSize=u,y.value.currentPage=1,q()},J=u=>{y.value.currentPage=u,q()};return Ie(()=>{Re(),q()}),(u,a)=>{const G=_("el-button"),f=_("el-input"),X=_("el-option"),ke=_("el-select"),K=_("el-table-column"),$e=_("el-tag"),Ce=_("el-table"),Ve=_("el-pagination"),Te=Ae("loading");return o(),n("div",us,[e("div",cs,[e("div",ps,[a[15]||(a[15]=Ne('<div class="flex items-center space-x-3"><div class="w-10 h-10 bg-gradient-to-br from-primary-500 to-primary-600 rounded-xl flex items-center justify-center"><svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path></svg></div><div><h1 class="text-2xl font-bold text-gray-900 dark:text-dark-text">工作流</h1><p class="mt-1 text-sm text-gray-600 dark:text-dark-text-secondary">查看工作流执行历史和管理工作流</p></div></div>',1)),e("div",ms,[s(G,{onClick:a[0]||(a[0]=b=>j.value=!0),type:"primary",size:"large",icon:V(We)},{default:d(()=>a[12]||(a[12]=[T(" 新建工作流 ")])),_:1,__:[12]},8,["icon"]),s(G,{onClick:a[1]||(a[1]=b=>D.value=!0),size:"large"},{default:d(()=>a[13]||(a[13]=[T(" 工作流模板 ")])),_:1,__:[13]}),s(G,{onClick:Y,size:"large",icon:V(Be)},{default:d(()=>a[14]||(a[14]=[T(" 导出 ")])),_:1,__:[14]},8,["icon"])])])]),e("div",gs,[e("div",xs,[e("div",ks,[e("div",null,[a[16]||(a[16]=e("p",{class:"text-sm font-medium text-gray-600 dark:text-dark-text-secondary"},"总执行次数",-1)),e("p",vs,l(p.value),1)]),a[17]||(a[17]=e("div",{class:"w-12 h-12 bg-blue-100 dark:bg-blue-900/30 rounded-lg flex items-center justify-center"},[e("svg",{class:"w-6 h-6 text-blue-600 dark:text-blue-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"})])],-1))])]),e("div",fs,[e("div",bs,[e("div",null,[a[18]||(a[18]=e("p",{class:"text-sm font-medium text-gray-600 dark:text-dark-text-secondary"},"成功率",-1)),e("p",ys,l(i.value)+"%",1)]),a[19]||(a[19]=e("div",{class:"w-12 h-12 bg-green-100 dark:bg-green-900/30 rounded-lg flex items-center justify-center"},[e("svg",{class:"w-6 h-6 text-green-600 dark:text-green-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"})])],-1))])]),e("div",_s,[e("div",ws,[e("div",null,[a[20]||(a[20]=e("p",{class:"text-sm font-medium text-gray-600 dark:text-dark-text-secondary"},"活跃工作流",-1)),e("p",hs,l(v.value),1)]),a[21]||(a[21]=e("div",{class:"w-12 h-12 bg-purple-100 dark:bg-purple-900/30 rounded-lg flex items-center justify-center"},[e("svg",{class:"w-6 h-6 text-purple-600 dark:text-purple-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M13 10V3L4 14h7v7l9-11h-7z"})])],-1))])]),e("div",$s,[e("div",Cs,[e("div",null,[a[22]||(a[22]=e("p",{class:"text-sm font-medium text-gray-600 dark:text-dark-text-secondary"},"平均耗时",-1)),e("p",Vs,l(r.value),1)]),a[23]||(a[23]=e("div",{class:"w-12 h-12 bg-orange-100 dark:bg-orange-900/30 rounded-lg flex items-center justify-center"},[e("svg",{class:"w-6 h-6 text-orange-600 dark:text-orange-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"})])],-1))])])]),e("div",Ts,[e("div",Ss,[e("div",Ds,[s(f,{modelValue:S.value,"onUpdate:modelValue":a[2]||(a[2]=b=>S.value=b),placeholder:"搜索工作流名称...",style:{width:"300px"},clearable:"",onInput:h},{prefix:d(()=>[s(V(be),{class:"w-4 h-4 text-gray-400"})]),_:1},8,["modelValue"]),s(ke,{modelValue:N.value,"onUpdate:modelValue":a[3]||(a[3]=b=>N.value=b),placeholder:"状态筛选",style:{width:"120px"},clearable:"",onChange:h},{default:d(()=>[s(X,{label:"全部",value:""}),s(X,{label:"启用",value:"enabled"}),s(X,{label:"禁用",value:"disabled"})]),_:1},8,["modelValue"])]),e("div",js,[e("div",zs," 共 "+l(y.value.total)+" 条执行记录 ",1),B.value.length>0?(o(),n("div",Ms,[e("span",Es,"已选择 "+l(B.value.length)+" 项",1),s(G,{onClick:a[4]||(a[4]=()=>V(H).info("批量操作功能开发中...")),type:"primary",size:"small",plain:""},{default:d(()=>a[24]||(a[24]=[T(" 批量启用/禁用 ")])),_:1,__:[24]})])):R("",!0)])])]),e("div",Is,[Le((o(),U(Ce,{data:w.value,onSelectionChange:C,class:"w-full","header-cell-style":{backgroundColor:"#f8fafc",color:"#374151",fontWeight:"600"}},{default:d(()=>[s(K,{type:"selection",width:"55"}),s(K,{prop:"id",label:"执行ID",width:"120"}),s(K,{prop:"workflowName",label:"工作流名称","min-width":"200"}),s(K,{label:"工作流程","min-width":"300"},{default:d(({row:b})=>[e("div",Ns,[e("div",Ws,[e("div",Bs,[e("div",Ls,[s(V(ye),{class:"w-3 h-3 text-white"})]),a[25]||(a[25]=e("span",{class:"text-xs text-gray-600 dark:text-dark-text-secondary"},"开始",-1))]),s(V(ae),{class:"w-4 h-4 text-gray-400 flex-shrink-0"}),e("div",As,[(o(!0),n(P,null,Z(b.workflow.apps,(ne,ve)=>{var we;return o(),n("div",{key:ve,class:"flex items-center space-x-1 flex-shrink-0"},[e("div",{class:de(["flex items-center space-x-1 rounded px-2 py-1",m((we=b.stepResults[ve])==null?void 0:we.status)])},[(o(),U(te(k(ne.type)),{class:"w-4 h-4"})),e("span",Rs,l(ne.name),1)],2),ve<b.workflow.apps.length-1?(o(),U(V(ae),{key:0,class:"w-3 h-3 text-gray-400"})):R("",!0)])}),128))]),s(V(ae),{class:"w-4 h-4 text-gray-400 flex-shrink-0"}),e("div",Us,[e("div",Ps,[s(V(_e),{class:"w-3 h-3 text-white"})]),a[26]||(a[26]=e("span",{class:"text-xs text-gray-600 dark:text-dark-text-secondary"},"结束",-1))])])])]),_:1}),s(K,{label:"执行状态",width:"120",align:"center"},{default:d(({row:b})=>[s($e,{type:F(b.status),size:"small"},{default:d(()=>[T(l(Q(b.status)),1)]),_:2},1032,["type"])]),_:1}),s(K,{prop:"duration",label:"执行时长",width:"120",align:"center"}),s(K,{prop:"executor",label:"执行人",width:"120"}),s(K,{prop:"startTime",label:"开始时间",width:"180"}),s(K,{label:"操作",width:"150",align:"center"},{default:d(({row:b})=>[e("div",Fs,[s(G,{onClick:ne=>x(b),type:"primary",size:"small",plain:""},{default:d(()=>a[27]||(a[27]=[T(" 查看详情 ")])),_:2,__:[27]},1032,["onClick"]),s(G,{onClick:ne=>t(b),type:"success",size:"small",plain:"",disabled:b.status!=="completed"},{default:d(()=>a[28]||(a[28]=[T(" 处理结果 ")])),_:2,__:[28]},1032,["onClick","disabled"])])]),_:1})]),_:1},8,["data"])),[[Te,L.value]]),e("div",Os,[s(Ve,{"current-page":y.value.currentPage,"onUpdate:currentPage":a[5]||(a[5]=b=>y.value.currentPage=b),"page-size":y.value.pageSize,"onUpdate:pageSize":a[6]||(a[6]=b=>y.value.pageSize=b),total:y.value.total,"page-sizes":[10,20,50,100],layout:"total, sizes, prev, pager, next, jumper",onSizeChange:xe,onCurrentChange:J},null,8,["current-page","page-size","total"])])]),s(Ot,{modelValue:j.value,"onUpdate:modelValue":a[7]||(a[7]=b=>j.value=b),onSuccess:le},null,8,["modelValue"]),s(he,{modelValue:D.value,"onUpdate:modelValue":a[8]||(a[8]=b=>D.value=b),onSelect:O,onCreateBlank:a[9]||(a[9]=b=>j.value=!0)},null,8,["modelValue"]),s(ya,{modelValue:$.value,"onUpdate:modelValue":a[10]||(a[10]=b=>$.value=b),execution:E.value,onViewResults:t},null,8,["modelValue","execution"]),s(is,{modelValue:g.value,"onUpdate:modelValue":a[11]||(a[11]=b=>g.value=b),execution:E.value},null,8,["modelValue","execution"])])}}});export{Xs as default};
