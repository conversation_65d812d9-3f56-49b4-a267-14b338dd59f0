import{e as _e,d as ae,r as T,f as A,g as se,h as I,j as n,k as y,a as e,l as o,c as g,m as $,n as ee,p as U,b as Y,q as le,s as ne,t as m,x as de,y as ie,z as re,F as te,A as k,B as j,C as Q,E as h,o as p,_ as oe,D as ue,G as Ce,H as Ve,I as $e,J as Me}from"./index-g0Lcbgij.js";import{r as ce}from"./MagnifyingGlassIcon-B6kQhNbF.js";var b=(u=>(u.PRODUCT="product",u.STORE="store",u.SEARCH="search",u.OTHER="other",u))(b||{}),E=(u=>(u.AMAZON="amazon",u.TEMU="temu",u.SHEIN="shein",u))(E||{});const z=_e({tasks:[{id:"COL001",type:"商品",platform:"亚马逊",targetCount:100,successCount:95,status:"已完成",collector:"admin",createTime:"2024-01-15 10:30:00",links:`https://amazon.com/product1
https://amazon.com/product2`},{id:"COL002",type:"店铺",platform:"Temu",targetCount:50,successCount:45,status:"部分失败",collector:"admin",createTime:"2024-01-14 14:20:00",links:"https://temu.com/store1",failureReason:"部分商品链接失效"},{id:"COL003",type:"搜索",platform:"Shein",targetCount:200,successCount:150,status:"进行中",collector:"admin",createTime:"2024-01-13 09:15:00",keyword:"women dress"},{id:"COL004",type:"商品",platform:"亚马逊",targetCount:30,successCount:0,status:"失败",collector:"admin",createTime:"2024-01-12 16:45:00",links:"https://amazon.com/invalid-product",failureReason:"所有商品链接均无效"}],pagination:{currentPage:1,pageSize:20,total:0},loading:!1}),Te=(u=1,_=20)=>(z.loading=!0,new Promise(M=>{setTimeout(()=>{const V=(u-1)*_,v=V+_,f=z.tasks.slice(V,v),a=z.tasks.length;z.pagination.currentPage=u,z.pagination.pageSize=_,z.pagination.total=a,z.loading=!1,M({data:f,total:a})},500)})),Se=u=>new Promise(_=>{setTimeout(()=>{var V;const M={id:`COL${String(z.tasks.length+1).padStart(3,"0")}`,type:je(u.type),platform:ze(u.platform),targetCount:u.type==="search"?100:((V=u.links)==null?void 0:V.split(`
`).filter(v=>v.trim()).length)||0,successCount:0,status:"待开始",collector:"admin",createTime:new Date().toLocaleString("zh-CN"),links:u.links,keyword:u.keyword};z.tasks.unshift(M),_(M)},1e3)}),je=u=>({product:"商品",store:"店铺",search:"搜索",other:"其他"})[u]||"未知",ze=u=>({amazon:"亚马逊",temu:"Temu",shein:"Shein"})[u]||"未知",De=(u="excel")=>new Promise(_=>{setTimeout(()=>{console.log(`导出${u}格式的采集数据...`),_()},1e3)}),Be=()=>new Promise(u=>{setTimeout(()=>{console.log("下载采集插件..."),u()},500)}),Ue={class:"p-6 space-y-6"},He={class:"space-y-3"},Re={class:"grid grid-cols-2 gap-3 w-full"},Oe=["value"],Pe={class:"flex items-center space-x-3"},Ee={class:"font-medium text-gray-900 dark:text-dark-text"},Ae={key:0,class:"absolute top-2 right-2 w-5 h-5 bg-primary-500 rounded-full flex items-center justify-center"},Le={key:0,class:"space-y-3"},Ie={class:"block text-sm font-semibold text-gray-900 dark:text-dark-text"},Ne={key:1,class:"space-y-3"},Ge={key:2,class:"space-y-3"},qe={class:"grid grid-cols-3 gap-3 w-full"},Ze=["value","onUpdate:modelValue"],Fe={class:"flex flex-col items-center space-y-1 w-full"},Je={class:"text-lg"},We={class:"text-sm font-medium text-gray-900 dark:text-dark-text"},Ke={key:0,class:"absolute top-1 right-1 w-4 h-4 bg-primary-500 rounded-full flex items-center justify-center"},Qe={key:3,class:"space-y-3"},Xe={class:"flex items-center space-x-3"},Ye={key:4,class:"space-y-3"},et={key:5,class:"space-y-3"},tt={key:6,class:"space-y-3"},rt={class:"p-4 bg-gray-50 dark:bg-dark-card rounded-lg border border-gray-200 dark:border-dark-border"},at={class:"flex items-center justify-end space-x-3 p-6 border-t border-gray-100 dark:border-dark-border bg-gray-50 dark:bg-dark-card/50"},ot=["disabled"],lt={key:0,class:"animate-spin -ml-1 mr-2 h-4 w-4 text-white",fill:"none",viewBox:"0 0 24 24"},st=ae({__name:"CreateCollectionDialog",props:{modelValue:{type:Boolean}},emits:["update:modelValue","success"],setup(u,{emit:_}){const M=u,V=_,v=T(),f=T(!1),a=T({type:b.PRODUCT,links:"",keyword:"",platform:E.AMAZON,priceMin:"",priceMax:"",pageCount:5,productType:""}),C=[{value:b.PRODUCT,label:"商品链接",icon:de},{value:b.STORE,label:"店铺链接",icon:ie},{value:b.SEARCH,label:"搜索采集",icon:ce},{value:b.OTHER,label:"其他方式",icon:re}],L=[{value:E.AMAZON,label:"亚马逊",icon:"🛒"},{value:E.TEMU,label:"Temu",icon:"🛍️"},{value:E.SHEIN,label:"Shein",icon:"👗"}],H=[{value:"",label:"全部类型"},{value:"new",label:"新品"},{value:"hot",label:"热卖"},{value:"discount",label:"折扣商品"},{value:"bestseller",label:"畅销商品"},{value:"featured",label:"精选商品"}],R=A({get:()=>M.modelValue,set:s=>V("update:modelValue",s)}),N=A(()=>a.value.type===b.PRODUCT||a.value.type===b.STORE?a.value.links.trim()!=="":a.value.type===b.SEARCH?a.value.keyword.trim()!==""&&a.value.platform:a.value.type===b.OTHER),G={type:[{required:!0,message:"请选择采集类型",trigger:"change"}],links:[{required:!0,message:"请输入链接地址",trigger:"blur"}],keyword:[{required:!0,message:"请输入搜索关键词",trigger:"blur"}],platform:[{required:!0,message:"请选择平台",trigger:"change"}]},q=()=>{a.value.links="",a.value.keyword="",a.value.platform=E.AMAZON,a.value.priceMin="",a.value.priceMax="",a.value.pageCount=5,a.value.productType=""},O=()=>{h.success("采集器下载功能开发中...")},P=async()=>{if(v.value)try{await v.value.validate(),f.value=!0,await Se(a.value),h.success("采集任务创建成功！"),V("success"),x()}catch(s){console.error("创建采集任务失败:",s),h.error("创建失败，请重试")}finally{f.value=!1}},x=()=>{v.value&&v.value.resetFields(),a.value={type:b.PRODUCT,links:"",keyword:"",platform:E.AMAZON,priceMin:"",priceMax:"",pageCount:5,productType:""},V("update:modelValue",!1)};return se(R,s=>{s&&(x(),V("update:modelValue",!0))}),(s,r)=>{const S=y("el-form-item"),B=y("el-input"),Z=y("el-input-number"),F=y("el-option"),J=y("el-select"),W=y("el-form"),c=y("el-dialog");return p(),I(c,{modelValue:R.value,"onUpdate:modelValue":r[7]||(r[7]=i=>R.value=i),width:"700px","before-close":x,"show-close":!1,class:"modern-dialog"},{header:n(()=>[e("div",{class:"flex items-center justify-between p-6 border-b border-gray-100 dark:border-dark-border"},[r[9]||(r[9]=e("div",{class:"flex items-center space-x-3"},[e("div",{class:"w-10 h-10 bg-gradient-to-br from-primary-500 to-primary-600 rounded-xl flex items-center justify-center"},[e("svg",{class:"w-6 h-6 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 6v6m0 0v6m0-6h6m-6 0H6"})])]),e("div",null,[e("h3",{class:"text-xl font-bold text-gray-900 dark:text-dark-text"},"新建采集任务"),e("p",{class:"text-sm text-gray-600 dark:text-dark-text-secondary"},"配置您的商品采集参数")])],-1)),e("button",{onClick:x,class:"p-2 text-gray-400 hover:text-gray-600 dark:text-dark-text-secondary dark:hover:text-dark-text rounded-lg hover:bg-gray-100 dark:hover:bg-dark-card transition-all duration-200"},r[8]||(r[8]=[e("svg",{class:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M6 18L18 6M6 6l12 12"})],-1)]))])]),footer:n(()=>[e("div",at,[e("button",{onClick:x,class:"px-6 py-2.5 text-gray-700 dark:text-dark-text font-medium rounded-lg border border-gray-300 dark:border-dark-border hover:bg-gray-50 dark:hover:bg-dark-border transition-all duration-200"}," 取消 "),e("button",{onClick:P,disabled:!N.value||f.value,class:"inline-flex items-center px-6 py-2.5 bg-gradient-to-r from-primary-500 to-primary-600 hover:from-primary-600 hover:to-primary-700 disabled:from-gray-400 disabled:to-gray-500 text-white font-medium rounded-lg shadow-lg hover:shadow-xl disabled:shadow-none transition-all duration-200 transform hover:scale-105 disabled:scale-100 disabled:cursor-not-allowed"},[f.value?(p(),g("svg",lt,r[26]||(r[26]=[e("circle",{class:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor","stroke-width":"4"},null,-1),e("path",{class:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"},null,-1)]))):$("",!0),j(" "+m(f.value?"创建中...":"开始采集"),1)],8,ot)])]),default:n(()=>[e("div",Ue,[o(W,{ref_key:"formRef",ref:v,model:a.value,rules:G,"label-width":"0px",class:"space-y-6"},{default:n(()=>[e("div",He,[r[11]||(r[11]=e("label",{class:"block text-sm font-semibold text-gray-900 dark:text-dark-text"}," 采集类型 ",-1)),o(S,{prop:"type",class:"mb-0"},{default:n(()=>[e("div",Re,[(p(),g(te,null,ee(C,i=>e("label",{key:i.value,class:U(["relative flex items-center p-4 border-2 rounded-xl cursor-pointer transition-all duration-200 hover:shadow-md",a.value.type===i.value?"border-primary-500 bg-primary-50 dark:bg-primary-900/20":"border-gray-200 dark:border-dark-border hover:border-gray-300 dark:hover:border-gray-600"])},[Y(e("input",{type:"radio",value:i.value,"onUpdate:modelValue":r[0]||(r[0]=K=>a.value.type=K),onChange:q,class:"sr-only"},null,40,Oe),[[le,a.value.type]]),e("div",Pe,[e("div",{class:U(["w-8 h-8 rounded-lg flex items-center justify-center",a.value.type===i.value?"bg-primary-500 text-white":"bg-gray-100 dark:bg-dark-card text-gray-600 dark:text-dark-text-secondary"])},[(p(),I(ne(i.icon),{class:"w-4 h-4"}))],2),e("span",Ee,m(i.label),1)]),a.value.type===i.value?(p(),g("div",Ae,r[10]||(r[10]=[e("svg",{class:"w-3 h-3 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M5 13l4 4L19 7"})],-1)]))):$("",!0)],2)),64))])]),_:1})]),a.value.type===k(b).PRODUCT||a.value.type===k(b).STORE?(p(),g("div",Le,[e("label",Ie,m(a.value.type===k(b).PRODUCT?"商品链接":"店铺链接"),1),o(S,{prop:"links",class:"mb-0"},{default:n(()=>[o(B,{modelValue:a.value.links,"onUpdate:modelValue":r[1]||(r[1]=i=>a.value.links=i),type:"textarea",rows:6,placeholder:a.value.type===k(b).PRODUCT?"请输入商品链接地址，一行一条":"请输入店铺链接地址，一行一条",class:"modern-textarea"},null,8,["modelValue","placeholder"]),r[12]||(r[12]=e("div",{class:"flex items-start space-x-2 mt-2 p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg border border-blue-200 dark:border-blue-800"},[e("svg",{class:"w-4 h-4 text-blue-500 mt-0.5 flex-shrink-0",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"})]),e("div",{class:"text-xs text-blue-700 dark:text-blue-300"},[e("p",{class:"font-medium"},"支持平台："),e("p",null,"亚马逊、Temu、Shein - 每行输入一个链接地址")])],-1))]),_:1,__:[12]})])):$("",!0),a.value.type===k(b).SEARCH?(p(),g("div",Ne,[r[13]||(r[13]=e("label",{class:"block text-sm font-semibold text-gray-900 dark:text-dark-text"}," 搜索关键词 ",-1)),o(S,{prop:"keyword",class:"mb-0"},{default:n(()=>[o(B,{modelValue:a.value.keyword,"onUpdate:modelValue":r[2]||(r[2]=i=>a.value.keyword=i),placeholder:"请输入搜索关键词，如：iPhone 15",class:"modern-input"},null,8,["modelValue"])]),_:1})])):$("",!0),a.value.type===k(b).SEARCH?(p(),g("div",Ge,[r[15]||(r[15]=e("label",{class:"block text-sm font-semibold text-gray-900 dark:text-dark-text"}," 选择平台 ",-1)),o(S,{prop:"platform",class:"mb-0"},{default:n(()=>[e("div",qe,[(p(),g(te,null,ee(L,i=>e("label",{key:i.value,class:U(["relative flex items-center p-3 border-2 rounded-lg cursor-pointer transition-all duration-200",a.value.platform===i.value?"border-primary-500 bg-primary-50 dark:bg-primary-900/20":"border-gray-200 dark:border-dark-border hover:border-gray-300 dark:hover:border-gray-600"])},[Y(e("input",{type:"radio",value:i.value,"onUpdate:modelValue":K=>a.value.platform=K,class:"sr-only"},null,8,Ze),[[le,a.value.platform]]),e("div",Fe,[e("span",Je,m(i.icon),1),e("span",We,m(i.label),1)]),a.value.platform===i.value?(p(),g("div",Ke,r[14]||(r[14]=[e("svg",{class:"w-2.5 h-2.5 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M5 13l4 4L19 7"})],-1)]))):$("",!0)],2)),64))])]),_:1})])):$("",!0),a.value.type===k(b).SEARCH?(p(),g("div",Qe,[r[19]||(r[19]=e("label",{class:"block text-sm font-semibold text-gray-900 dark:text-dark-text"}," 价格区间 ",-1)),o(S,{class:"mb-0"},{default:n(()=>[e("div",Xe,[o(B,{modelValue:a.value.priceMin,"onUpdate:modelValue":r[3]||(r[3]=i=>a.value.priceMin=i),placeholder:"最低价格",type:"number",class:"modern-input",style:{width:"150px"}},{prefix:n(()=>r[16]||(r[16]=[j("¥")])),_:1},8,["modelValue"]),r[18]||(r[18]=e("span",{class:"text-gray-500 dark:text-dark-text-secondary"},"至",-1)),o(B,{modelValue:a.value.priceMax,"onUpdate:modelValue":r[4]||(r[4]=i=>a.value.priceMax=i),placeholder:"最高价格",type:"number",class:"modern-input",style:{width:"150px"}},{prefix:n(()=>r[17]||(r[17]=[j("¥")])),_:1},8,["modelValue"])])]),_:1})])):$("",!0),a.value.type===k(b).SEARCH?(p(),g("div",Ye,[r[21]||(r[21]=e("label",{class:"block text-sm font-semibold text-gray-900 dark:text-dark-text"}," 翻页数 ",-1)),o(S,{prop:"pageCount",class:"mb-0"},{default:n(()=>[o(Z,{modelValue:a.value.pageCount,"onUpdate:modelValue":r[5]||(r[5]=i=>a.value.pageCount=i),min:1,max:50,placeholder:"请输入翻页数",class:"w-full"},null,8,["modelValue"]),r[20]||(r[20]=e("div",{class:"mt-1 text-xs text-gray-500 dark:text-dark-text-secondary"}," 建议设置1-10页，页数过多可能影响采集效率 ",-1))]),_:1,__:[20]})])):$("",!0),a.value.type===k(b).SEARCH?(p(),g("div",et,[r[22]||(r[22]=e("label",{class:"block text-sm font-semibold text-gray-900 dark:text-dark-text"}," 商品类型 ",-1)),o(S,{prop:"productType",class:"mb-0"},{default:n(()=>[o(J,{modelValue:a.value.productType,"onUpdate:modelValue":r[6]||(r[6]=i=>a.value.productType=i),placeholder:"请选择商品类型",class:"w-full"},{default:n(()=>[(p(),g(te,null,ee(H,i=>o(F,{key:i.value,label:i.label,value:i.value},null,8,["label","value"])),64))]),_:1},8,["modelValue"])]),_:1})])):$("",!0),a.value.type===k(b).OTHER?(p(),g("div",tt,[r[25]||(r[25]=e("label",{class:"block text-sm font-semibold text-gray-900 dark:text-dark-text"}," 采集器工具 ",-1)),e("div",rt,[e("button",{onClick:O,class:"inline-flex items-center px-4 py-2.5 bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700 text-white font-medium rounded-lg shadow-lg hover:shadow-xl transition-all duration-200 transform hover:scale-105"},[o(k(Q),{class:"w-5 h-5 mr-2"}),r[23]||(r[23]=j(" 下载采集器 "))]),r[24]||(r[24]=e("div",{class:"mt-3 p-3 bg-yellow-50 dark:bg-yellow-900/20 rounded-lg border border-yellow-200 dark:border-yellow-800"},[e("div",{class:"flex items-start space-x-2"},[e("svg",{class:"w-4 h-4 text-yellow-500 mt-0.5 flex-shrink-0",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"})]),e("div",{class:"text-xs text-yellow-700 dark:text-yellow-300"},[e("p",{class:"font-medium"},"使用说明："),e("p",null,"1. 下载并安装采集器工具"),e("p",null,"2. 按照安装向导完成配置"),e("p",null,"3. 启动采集器开始自定义采集")])])],-1))])])):$("",!0)]),_:1},8,["model"])])]),_:1},8,["modelValue"])}}}),nt=oe(st,[["__scopeId","data-v-9a03c61e"]]),dt={class:"flex items-center justify-between p-6 border-b border-gray-100 dark:border-dark-border"},it={class:"flex items-center space-x-3"},ut={class:"text-sm text-gray-600 dark:text-dark-text-secondary"},ct={key:0,class:"p-6 grid grid-cols-2 md:grid-cols-4 gap-4"},pt={class:"bg-gradient-to-br from-blue-50 to-blue-100 dark:from-blue-900/20 dark:to-blue-800/20 p-4 rounded-xl border border-blue-200 dark:border-blue-800"},mt={class:"flex items-center space-x-2"},gt={class:"text-sm font-bold text-blue-900 dark:text-blue-100"},xt={class:"bg-gradient-to-br from-green-50 to-green-100 dark:from-green-900/20 dark:to-green-800/20 p-4 rounded-xl border border-green-200 dark:border-green-800"},kt={class:"flex items-center space-x-2"},vt={class:"text-sm font-bold text-green-900 dark:text-green-100"},bt={class:"bg-gradient-to-br from-purple-50 to-purple-100 dark:from-purple-900/20 dark:to-purple-800/20 p-4 rounded-xl border border-purple-200 dark:border-purple-800"},ht={class:"flex items-center space-x-2"},yt={class:"text-sm font-bold text-purple-900 dark:text-purple-100"},ft={class:"bg-gradient-to-br from-emerald-50 to-emerald-100 dark:from-emerald-900/20 dark:to-emerald-800/20 p-4 rounded-xl border border-emerald-200 dark:border-emerald-800"},wt={class:"flex items-center space-x-2"},_t={class:"text-sm font-bold text-emerald-900 dark:text-emerald-100"},Ct={class:"px-6 pb-6"},Vt={class:"bg-white dark:bg-dark-surface rounded-xl border border-gray-200 dark:border-dark-border overflow-hidden"},$t={key:0,class:"flex justify-center"},Mt={key:1,class:"text-gray-400 text-xs"},Tt={key:0,class:"text-sm font-medium text-gray-900 dark:text-dark-text"},St={key:1,class:"text-gray-400 text-xs"},jt={key:0,class:"text-red-600 dark:text-red-400 font-medium"},zt={key:1,class:"text-gray-400 text-xs"},Dt={key:0,class:"flex items-center justify-center"},Bt={key:1,class:"text-gray-400 text-xs"},Ut=["onClick"],Ht={class:"flex justify-center p-4 border-t border-gray-200 dark:border-dark-border"},Rt={class:"flex items-center justify-between p-6 border-t border-gray-100 dark:border-dark-border bg-gray-50 dark:bg-dark-card/50"},Ot={class:"text-sm text-gray-600 dark:text-dark-text-secondary"},Pt={class:"flex items-center space-x-3"},Et=ae({__name:"ViewDetailsDialog",props:{modelValue:{type:Boolean},collectionData:{}},emits:["update:modelValue"],setup(u,{emit:_}){const M=u,V=_,v=T(!1),f=T(1),a=T(20),C=T(0),L=T([{index:1,title:"Apple iPhone 15 Pro Max 256GB Natural Titanium",mainImage:"https://images.unsplash.com/photo-1592750475338-74b7b21085ab?w=200&h=200&fit=crop",price:"$1,199.00",rating:4.5,status:"成功",originalUrl:"https://amazon.com/example1"},{index:2,title:"Samsung Galaxy S24 Ultra 512GB Titanium Black",mainImage:"https://images.unsplash.com/photo-1511707171634-5f897ff02aa9?w=200&h=200&fit=crop",price:"$1,299.99",rating:4.3,status:"成功",originalUrl:"https://amazon.com/example2"},{index:3,title:"",mainImage:"",price:"",rating:0,status:"失败",originalUrl:"https://amazon.com/example3"}]),H=A({get:()=>M.modelValue,set:x=>V("update:modelValue",x)}),R=x=>{window.open(x,"_blank")},N=()=>{h.success("导出详情功能开发中...")},G=x=>{a.value=x,O()},q=x=>{f.value=x,O()},O=()=>{v.value=!0,setTimeout(()=>{C.value=L.value.length,v.value=!1},300)},P=()=>{V("update:modelValue",!1)};return se(H,x=>{x&&M.collectionData&&(f.value=1,O())}),(x,s)=>{const r=y("el-table-column"),S=y("el-image"),B=y("el-rate"),Z=y("el-table"),F=y("el-pagination"),J=y("el-dialog"),W=ue("loading");return p(),I(J,{modelValue:H.value,"onUpdate:modelValue":s[2]||(s[2]=c=>H.value=c),width:"1200px","before-close":P,"show-close":!1,class:"modern-dialog"},{header:n(()=>{var c;return[e("div",dt,[e("div",it,[s[4]||(s[4]=e("div",{class:"w-10 h-10 bg-gradient-to-br from-blue-500 to-blue-600 rounded-xl flex items-center justify-center"},[e("svg",{class:"w-6 h-6 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"})])],-1)),e("div",null,[s[3]||(s[3]=e("h3",{class:"text-xl font-bold text-gray-900 dark:text-dark-text"},"采集详情",-1)),e("p",ut,"任务ID: "+m(((c=x.collectionData)==null?void 0:c.id)||""),1)])]),e("button",{onClick:P,class:"p-2 text-gray-400 hover:text-gray-600 dark:text-dark-text-secondary dark:hover:text-dark-text rounded-lg hover:bg-gray-100 dark:hover:bg-dark-card transition-all duration-200"},s[5]||(s[5]=[e("svg",{class:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M6 18L18 6M6 6l12 12"})],-1)]))])]}),footer:n(()=>[e("div",Rt,[e("div",Ot," 共 "+m(C.value)+" 条采集结果 ",1),e("div",Pt,[e("button",{onClick:P,class:"px-6 py-2.5 text-gray-700 dark:text-dark-text font-medium rounded-lg border border-gray-300 dark:border-dark-border hover:bg-gray-50 dark:hover:bg-dark-border transition-all duration-200"}," 关闭 "),e("button",{onClick:N,class:"inline-flex items-center px-6 py-2.5 bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700 text-white font-medium rounded-lg shadow-lg hover:shadow-xl transition-all duration-200 transform hover:scale-105"},[o(k(Q),{class:"w-5 h-5 mr-2"}),s[15]||(s[15]=j(" 导出详情 "))])])])]),default:n(()=>[x.collectionData?(p(),g("div",ct,[e("div",pt,[e("div",mt,[s[7]||(s[7]=e("div",{class:"w-8 h-8 bg-blue-500 rounded-lg flex items-center justify-center"},[e("svg",{class:"w-4 h-4 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a1.994 1.994 0 01-1.414.586H7a4 4 0 01-4-4V7a4 4 0 014-4z"})])],-1)),e("div",null,[s[6]||(s[6]=e("p",{class:"text-xs text-blue-600 dark:text-blue-400 font-medium"},"采集类型",-1)),e("p",gt,m(x.collectionData.type),1)])])]),e("div",xt,[e("div",kt,[s[9]||(s[9]=e("div",{class:"w-8 h-8 bg-green-500 rounded-lg flex items-center justify-center"},[e("svg",{class:"w-4 h-4 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"})])],-1)),e("div",null,[s[8]||(s[8]=e("p",{class:"text-xs text-green-600 dark:text-green-400 font-medium"},"平台",-1)),e("p",vt,m(x.collectionData.platform),1)])])]),e("div",bt,[e("div",ht,[s[11]||(s[11]=e("div",{class:"w-8 h-8 bg-purple-500 rounded-lg flex items-center justify-center"},[e("svg",{class:"w-4 h-4 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"})])],-1)),e("div",null,[s[10]||(s[10]=e("p",{class:"text-xs text-purple-600 dark:text-purple-400 font-medium"},"目标数量",-1)),e("p",yt,m(x.collectionData.targetCount),1)])])]),e("div",ft,[e("div",wt,[s[13]||(s[13]=e("div",{class:"w-8 h-8 bg-emerald-500 rounded-lg flex items-center justify-center"},[e("svg",{class:"w-4 h-4 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"})])],-1)),e("div",null,[s[12]||(s[12]=e("p",{class:"text-xs text-emerald-600 dark:text-emerald-400 font-medium"},"成功数量",-1)),e("p",_t,m(x.collectionData.successCount),1)])])])])):$("",!0),e("div",Ct,[s[14]||(s[14]=e("h3",{class:"text-lg font-semibold text-gray-900 dark:text-dark-text mb-4"},"采集结果",-1)),e("div",Vt,[Y((p(),I(Z,{data:L.value,style:{width:"100%"},"max-height":"400",class:"modern-table"},{default:n(()=>[o(r,{prop:"index",label:"序号",width:"80",align:"center"}),o(r,{label:"主图",width:"100",align:"center"},{default:n(c=>[c.row.mainImage?(p(),g("div",$t,[o(S,{src:c.row.mainImage,"preview-src-list":[c.row.mainImage],fit:"cover",class:"w-16 h-16 rounded-lg border border-gray-200 dark:border-dark-border","preview-teleported":!0},null,8,["src","preview-src-list"])])):(p(),g("div",Mt,"无图片"))]),_:1}),o(r,{label:"标题","min-width":"200"},{default:n(c=>[c.row.title?(p(),g("div",Tt,m(c.row.title),1)):(p(),g("div",St,"无标题"))]),_:1}),o(r,{prop:"price",label:"价格",width:"100",align:"center"},{default:n(c=>[c.row.price?(p(),g("span",jt,m(c.row.price),1)):(p(),g("span",zt,"-"))]),_:1}),o(r,{prop:"rating",label:"评分",width:"120",align:"center"},{default:n(c=>[c.row.rating?(p(),g("div",Dt,[o(B,{modelValue:c.row.rating,"onUpdate:modelValue":i=>c.row.rating=i,disabled:"","show-score":"","text-color":"#ff9900","score-template":"{value}",size:"small"},null,8,["modelValue","onUpdate:modelValue"])])):(p(),g("span",Bt,"-"))]),_:1}),o(r,{prop:"status",label:"状态",width:"100",align:"center"},{default:n(c=>[e("span",{class:U(["inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium",c.row.status==="成功"?"bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-300":"bg-red-100 dark:bg-red-900/30 text-red-800 dark:text-red-300"])},m(c.row.status),3)]),_:1}),o(r,{label:"操作",width:"120",align:"center"},{default:n(c=>[c.row.originalUrl?(p(),g("button",{key:0,onClick:i=>R(c.row.originalUrl),class:"text-primary-600 dark:text-primary-400 hover:text-primary-700 dark:hover:text-primary-300 text-sm font-medium"}," 查看原链接 ",8,Ut)):$("",!0)]),_:1})]),_:1},8,["data"])),[[W,v.value]]),e("div",Ht,[o(F,{"current-page":f.value,"onUpdate:currentPage":s[0]||(s[0]=c=>f.value=c),"page-size":a.value,"onUpdate:pageSize":s[1]||(s[1]=c=>a.value=c),"page-sizes":[10,20,50],total:C.value,layout:"total, sizes, prev, pager, next",onSizeChange:G,onCurrentChange:q},null,8,["current-page","page-size","total"])])])])]),_:1},8,["modelValue"])}}}),At=oe(Et,[["__scopeId","data-v-88a244da"]]),Lt={class:"space-y-6"},It={class:"grid grid-cols-1 md:grid-cols-4 gap-6"},Nt={class:"bg-white dark:bg-dark-surface rounded-xl p-6 shadow-elegant dark:shadow-elegant-dark border border-gray-100 dark:border-dark-border hover:shadow-lg dark:hover:shadow-2xl transition-all duration-300"},Gt={class:"flex items-center justify-between"},qt={class:"text-2xl font-bold text-gray-900 dark:text-dark-text"},Zt={class:"bg-white dark:bg-dark-surface rounded-xl p-6 shadow-elegant dark:shadow-elegant-dark border border-gray-100 dark:border-dark-border hover:shadow-lg dark:hover:shadow-2xl transition-all duration-300"},Ft={class:"flex items-center justify-between"},Jt={class:"text-2xl font-bold text-green-600 dark:text-green-400"},Wt={class:"bg-white dark:bg-dark-surface rounded-xl p-6 shadow-elegant dark:shadow-elegant-dark border border-gray-100 dark:border-dark-border hover:shadow-lg dark:hover:shadow-2xl transition-all duration-300"},Kt={class:"flex items-center justify-between"},Qt={class:"text-2xl font-bold text-yellow-600 dark:text-yellow-400"},Xt={class:"bg-white dark:bg-dark-surface rounded-xl p-6 shadow-elegant dark:shadow-elegant-dark border border-gray-100 dark:border-dark-border hover:shadow-lg dark:hover:shadow-2xl transition-all duration-300"},Yt={class:"flex items-center justify-between"},er={class:"text-2xl font-bold text-purple-600 dark:text-purple-400"},tr={class:"bg-white dark:bg-dark-surface rounded-xl p-6 shadow-elegant dark:shadow-elegant-dark border border-gray-100 dark:border-dark-border"},rr={class:"flex justify-between items-center"},ar={class:"flex items-center space-x-3"},or={key:0,class:"flex items-center space-x-2 ml-4 pl-4 border-l border-gray-200 dark:border-dark-border"},lr={class:"text-sm text-gray-600 dark:text-dark-text-secondary"},sr={class:"bg-white dark:bg-dark-surface rounded-xl shadow-elegant dark:shadow-elegant-dark border border-gray-100 dark:border-dark-border overflow-hidden"},nr={class:"overflow-x-auto"},dr={class:"font-mono text-sm font-medium text-primary-600 dark:text-primary-400"},ir={class:"flex items-center space-x-3"},ur={class:"font-medium text-gray-900 dark:text-dark-text"},cr={class:"text-sm text-gray-500 dark:text-dark-text-secondary"},pr={class:"space-y-1"},mr={class:"flex items-center space-x-2"},gr={class:"font-medium text-gray-900 dark:text-dark-text"},xr={class:"flex items-center space-x-2"},kr={class:"font-medium text-green-600 dark:text-green-400"},vr={class:"w-full bg-gray-200 dark:bg-gray-700 rounded-full h-1.5"},br={class:"space-y-2"},hr={key:0},yr=["onClick"],fr={class:"flex items-center space-x-2"},wr={class:"w-6 h-6 bg-gradient-to-br from-primary-500 to-primary-600 rounded-full flex items-center justify-center"},_r={class:"text-white text-xs font-medium"},Cr={class:"text-sm font-medium text-gray-900 dark:text-dark-text"},Vr={class:"text-sm text-gray-600 dark:text-dark-text-secondary"},$r={class:"flex items-center space-x-2"},Mr=["onClick"],Tr={class:"flex justify-between items-center px-6 py-4 border-t border-gray-100 dark:border-dark-border bg-gray-50 dark:bg-dark-card/50"},Sr={class:"text-sm text-gray-600 dark:text-dark-text-secondary"},jr=ae({__name:"index",setup(u){const _=T(!1),M=T(!1),V=T(null),v=T([]),f=T([]),{loading:a,pagination:C}=z,L=A(()=>v.value.length),H=A(()=>{if(v.value.length===0)return 0;const l=v.value.reduce((w,D)=>w+D.successCount,0),t=v.value.reduce((w,D)=>w+D.targetCount,0);return t>0?Math.round(l/t*100):0}),R=A(()=>v.value.filter(l=>l.status==="进行中").length),N=A(()=>{const l=new Date().toDateString();return v.value.filter(t=>new Date(t.createTime).toDateString()===l).length}),G={商品:de,店铺:ie,搜索:ce,其他:re},q=l=>G[l]||re,O=l=>({商品:"bg-blue-100 dark:bg-blue-900/30",店铺:"bg-green-100 dark:bg-green-900/30",搜索:"bg-purple-100 dark:bg-purple-900/30",其他:"bg-gray-100 dark:bg-gray-900/30"})[l]||"bg-gray-100 dark:bg-gray-900/30",P=l=>({商品:"text-blue-600 dark:text-blue-400",店铺:"text-green-600 dark:text-green-400",搜索:"text-purple-600 dark:text-purple-400",其他:"text-gray-600 dark:text-gray-400"})[l]||"text-gray-600 dark:text-gray-400",x=l=>l==="已完成"?"bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-300":l==="进行中"?"bg-yellow-100 dark:bg-yellow-900/30 text-yellow-800 dark:text-yellow-300":l.includes("失败")?"bg-red-100 dark:bg-red-900/30 text-red-800 dark:text-red-300":"bg-gray-100 dark:bg-gray-900/30 text-gray-800 dark:text-gray-300",s=l=>l==="已完成"?"bg-green-500":l==="进行中"?"bg-yellow-500":l.includes("失败")?"bg-red-500":"bg-gray-500",r=async()=>{try{await De("excel"),h.success("表格导出成功！")}catch{h.error("导出失败，请重试")}},S=async()=>{try{await Be(),h.success("插件下载成功！")}catch{h.error("下载失败，请重试")}},B=l=>{const t=l.failureReason||"网络超时导致部分商品采集失败";h.info(`采集ID ${l.id} 的失败原因：${t}`)},Z=l=>{V.value=l,M.value=!0},F=()=>{X(),h.success("采集任务创建成功！")},J=l=>{const{action:t,row:w}=l;switch(t){case"smartCrop":W(w);break;case"titleGenerate":c(w);break;case"batchListing":i(w);break;case"oneClickCutout":K(w);break;case"superSplit":pe(w);break;default:h.warning("未知操作")}},W=l=>{h.success(`正在为采集任务 ${l.id} 创建智能裁图任务...`)},c=l=>{h.success(`正在为采集任务 ${l.id} 创建标题生成任务...`)},i=l=>{h.success(`正在为采集任务 ${l.id} 创建批量刊登任务...`)},K=l=>{h.success(`正在为采集任务 ${l.id} 创建一键抠图任务...`)},pe=l=>{h.success(`正在为采集任务 ${l.id} 创建超级裂变任务...`)},me=l=>{C.pageSize=l,X()},ge=l=>{C.currentPage=l,X()},X=async()=>{try{const{data:l,total:t}=await Te(C.currentPage,C.pageSize);v.value=l,C.total=t}catch{h.error("加载数据失败，请重试")}},xe=l=>{f.value=l},ke=()=>{if(f.value.length===0){h.warning("请先选择要导出的任务");return}h.success(`正在导出 ${f.value.length} 个任务的数据...`)};return Ce(()=>{X()}),(l,t)=>{const w=y("el-table-column"),D=y("el-dropdown-item"),ve=y("el-dropdown-menu"),be=y("el-dropdown"),he=y("el-table"),ye=y("el-pagination"),fe=ue("loading");return p(),g("div",Lt,[t[26]||(t[26]=Ve('<div class="bg-gradient-to-r from-primary-50 to-blue-50 dark:from-primary-900/20 dark:to-blue-900/20 rounded-2xl p-6 border border-primary-100 dark:border-primary-800" data-v-698f84e1><div class="flex items-center space-x-3" data-v-698f84e1><div class="w-10 h-10 bg-gradient-to-br from-primary-500 to-primary-600 rounded-xl flex items-center justify-center" data-v-698f84e1><svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24" data-v-698f84e1><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" data-v-698f84e1></path></svg></div><div data-v-698f84e1><h1 class="text-2xl font-bold text-gray-900 dark:text-dark-text" data-v-698f84e1>商品采集</h1><p class="mt-1 text-sm text-gray-600 dark:text-dark-text-secondary" data-v-698f84e1>智能采集全球电商平台商品信息</p></div></div></div>',1)),e("div",It,[e("div",Nt,[e("div",Gt,[e("div",null,[t[5]||(t[5]=e("p",{class:"text-sm font-medium text-gray-600 dark:text-dark-text-secondary"},"总采集数",-1)),e("p",qt,m(L.value),1)]),t[6]||(t[6]=e("div",{class:"w-12 h-12 bg-blue-100 dark:bg-blue-900/30 rounded-lg flex items-center justify-center"},[e("svg",{class:"w-6 h-6 text-blue-600 dark:text-blue-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"})])],-1))])]),e("div",Zt,[e("div",Ft,[e("div",null,[t[7]||(t[7]=e("p",{class:"text-sm font-medium text-gray-600 dark:text-dark-text-secondary"},"成功率",-1)),e("p",Jt,m(H.value)+"%",1)]),t[8]||(t[8]=e("div",{class:"w-12 h-12 bg-green-100 dark:bg-green-900/30 rounded-lg flex items-center justify-center"},[e("svg",{class:"w-6 h-6 text-green-600 dark:text-green-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"})])],-1))])]),e("div",Wt,[e("div",Kt,[e("div",null,[t[9]||(t[9]=e("p",{class:"text-sm font-medium text-gray-600 dark:text-dark-text-secondary"},"进行中",-1)),e("p",Qt,m(R.value),1)]),t[10]||(t[10]=e("div",{class:"w-12 h-12 bg-yellow-100 dark:bg-yellow-900/30 rounded-lg flex items-center justify-center"},[e("svg",{class:"w-6 h-6 text-yellow-600 dark:text-yellow-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"})])],-1))])]),e("div",Xt,[e("div",Yt,[e("div",null,[t[11]||(t[11]=e("p",{class:"text-sm font-medium text-gray-600 dark:text-dark-text-secondary"},"今日采集",-1)),e("p",er,m(N.value),1)]),t[12]||(t[12]=e("div",{class:"w-12 h-12 bg-purple-100 dark:bg-purple-900/30 rounded-lg flex items-center justify-center"},[e("svg",{class:"w-6 h-6 text-purple-600 dark:text-purple-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"})])],-1))])])]),e("div",tr,[e("div",rr,[e("div",ar,[e("button",{onClick:t[0]||(t[0]=d=>_.value=!0),class:"inline-flex items-center px-4 py-2.5 bg-gradient-to-r from-primary-500 to-primary-600 hover:from-primary-600 hover:to-primary-700 text-white font-medium rounded-lg shadow-lg hover:shadow-xl transition-all duration-200 transform hover:scale-105"},[o(k($e),{class:"w-5 h-5 mr-2"}),t[13]||(t[13]=j(" 新建采集 "))]),e("button",{onClick:r,class:"inline-flex items-center px-4 py-2.5 bg-white dark:bg-dark-card text-gray-700 dark:text-dark-text font-medium rounded-lg border border-gray-300 dark:border-dark-border hover:bg-gray-50 dark:hover:bg-dark-border shadow-sm hover:shadow-md transition-all duration-200"},[o(k(Q),{class:"w-5 h-5 mr-2"}),t[14]||(t[14]=j(" 导出表格 "))]),f.value.length>0?(p(),g("div",or,[e("span",lr," 已选择 "+m(f.value.length)+" 项 ",1),e("button",{onClick:ke,class:"inline-flex items-center px-3 py-1.5 bg-blue-500 hover:bg-blue-600 text-white text-sm font-medium rounded-lg transition-all duration-200"},[o(k(Q),{class:"w-4 h-4 mr-1"}),t[15]||(t[15]=j(" 批量导出 "))])])):$("",!0)]),e("div",null,[e("button",{onClick:S,class:"inline-flex items-center px-4 py-2.5 bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700 text-white font-medium rounded-lg shadow-lg hover:shadow-xl transition-all duration-200 transform hover:scale-105"},[o(k(Q),{class:"w-5 h-5 mr-2"}),t[16]||(t[16]=j(" 下载采集插件 "))])])])]),e("div",sr,[t[25]||(t[25]=e("div",{class:"px-6 py-4 border-b border-gray-100 dark:border-dark-border"},[e("h3",{class:"text-lg font-semibold text-gray-900 dark:text-dark-text"},"采集任务列表"),e("p",{class:"text-sm text-gray-600 dark:text-dark-text-secondary mt-1"},"管理和监控您的所有采集任务")],-1)),e("div",nr,[Y((p(),I(he,{data:v.value,style:{width:"100%"},"header-cell-style":{backgroundColor:"var(--el-bg-color-page)",color:"var(--el-text-color-primary)",fontWeight:"600",borderBottom:"1px solid var(--el-border-color-light)"},"row-style":{backgroundColor:"transparent"},class:"modern-table",onSelectionChange:xe},{default:n(()=>[o(w,{type:"selection",width:"55",align:"center"}),o(w,{prop:"id",label:"采集ID",width:"120"},{default:n(d=>[e("div",dr,m(d.row.id),1)]),_:1}),o(w,{label:"采集类型",width:"200"},{default:n(d=>[e("div",ir,[e("div",{class:U(["w-8 h-8 rounded-lg flex items-center justify-center",O(d.row.type)])},[(p(),I(ne(q(d.row.type)),{class:U(["w-4 h-4",P(d.row.type)])},null,8,["class"]))],2),e("div",null,[e("div",ur,m(d.row.type),1),e("div",cr,m(d.row.platform),1)])])]),_:1}),o(w,{label:"采集数量",width:"150"},{default:n(d=>[e("div",pr,[e("div",mr,[t[17]||(t[17]=e("span",{class:"text-xs text-gray-500 dark:text-dark-text-secondary"},"目标:",-1)),e("span",gr,m(d.row.targetCount),1)]),e("div",xr,[t[18]||(t[18]=e("span",{class:"text-xs text-gray-500 dark:text-dark-text-secondary"},"成功:",-1)),e("span",kr,m(d.row.successCount),1)]),e("div",vr,[e("div",{class:"bg-green-500 h-1.5 rounded-full transition-all duration-300",style:Me({width:`${d.row.successCount/d.row.targetCount*100}%`})},null,4)])])]),_:1}),o(w,{label:"采集状态",width:"150"},{default:n(d=>[e("div",br,[e("span",{class:U(["inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium",x(d.row.status)])},[e("span",{class:U(["w-1.5 h-1.5 rounded-full mr-1.5",s(d.row.status)])},null,2),j(" "+m(d.row.status),1)],2),d.row.status.includes("失败")?(p(),g("div",hr,[e("button",{onClick:we=>B(d.row),class:"text-xs text-red-600 dark:text-red-400 hover:text-red-700 dark:hover:text-red-300 underline"}," 查看原因 ",8,yr)])):$("",!0)])]),_:1}),o(w,{prop:"collector",label:"创建人",width:"100"},{default:n(d=>[e("div",fr,[e("div",wr,[e("span",_r,m(d.row.collector.charAt(0).toUpperCase()),1)]),e("span",Cr,m(d.row.collector),1)])]),_:1}),o(w,{prop:"createTime",label:"创建时间",width:"180"},{default:n(d=>[e("div",Vr,m(d.row.createTime),1)]),_:1}),o(w,{label:"操作",width:"180"},{default:n(d=>[e("div",$r,[e("button",{onClick:we=>Z(d.row),class:"inline-flex items-center px-3 py-1.5 text-sm font-medium text-primary-600 dark:text-primary-400 hover:text-primary-700 dark:hover:text-primary-300 bg-primary-50 dark:bg-primary-900/20 hover:bg-primary-100 dark:hover:bg-primary-900/30 rounded-lg transition-all duration-200"}," 查看详情 ",8,Mr),o(be,{onCommand:J,trigger:"click"},{dropdown:n(()=>[o(ve,null,{default:n(()=>[o(D,{command:{action:"smartCrop",row:d.row}},{default:n(()=>t[19]||(t[19]=[e("div",{class:"flex items-center space-x-2"},[e("svg",{class:"w-4 h-4 text-green-500",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M7 4V2a1 1 0 011-1h8a1 1 0 011 1v2m0 0V1a1 1 0 011-1h2a1 1 0 011 1v18a1 1 0 01-1 1H4a1 1 0 01-1-1V1a1 1 0 011-1h2a1 1 0 011 1v3m0 0h8m-8 0H4a1 1 0 00-1 1v3a1 1 0 001 1h3m0 0h10m0 0h3a1 1 0 001-1V5a1 1 0 00-1-1h-3m-3 0v8a1 1 0 01-1 1H8a1 1 0 01-1-1V4"})]),e("span",null,"智能裁图")],-1)])),_:2,__:[19]},1032,["command"]),o(D,{command:{action:"titleGenerate",row:d.row}},{default:n(()=>t[20]||(t[20]=[e("div",{class:"flex items-center space-x-2"},[e("svg",{class:"w-4 h-4 text-blue-500",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"})]),e("span",null,"标题生成")],-1)])),_:2,__:[20]},1032,["command"]),o(D,{command:{action:"batchListing",row:d.row}},{default:n(()=>t[21]||(t[21]=[e("div",{class:"flex items-center space-x-2"},[e("svg",{class:"w-4 h-4 text-purple-500",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"})]),e("span",null,"批量刊登")],-1)])),_:2,__:[21]},1032,["command"]),o(D,{command:{action:"oneClickCutout",row:d.row}},{default:n(()=>t[22]||(t[22]=[e("div",{class:"flex items-center space-x-2"},[e("svg",{class:"w-4 h-4 text-pink-500",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"})]),e("span",null,"一键抠图")],-1)])),_:2,__:[22]},1032,["command"]),o(D,{command:{action:"superSplit",row:d.row}},{default:n(()=>t[23]||(t[23]=[e("div",{class:"flex items-center space-x-2"},[e("svg",{class:"w-4 h-4 text-orange-500",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M13 10V3L4 14h7v7l9-11h-7z"})]),e("span",null,"超级裂变")],-1)])),_:2,__:[23]},1032,["command"])]),_:2},1024)]),default:n(()=>[t[24]||(t[24]=e("button",{class:"inline-flex items-center px-2 py-1.5 text-sm font-medium text-gray-600 dark:text-dark-text-secondary hover:text-gray-700 dark:hover:text-dark-text bg-gray-50 dark:bg-dark-card hover:bg-gray-100 dark:hover:bg-dark-border rounded-lg transition-all duration-200"},[j(" 更多 "),e("svg",{class:"w-4 h-4 ml-1",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M19 9l-7 7-7-7"})])],-1))]),_:2,__:[24]},1024)])]),_:1})]),_:1},8,["data"])),[[fe,k(a)]])]),e("div",Tr,[e("div",Sr," 共 "+m(k(C).total)+" 条记录 ",1),o(ye,{"current-page":k(C).currentPage,"onUpdate:currentPage":t[1]||(t[1]=d=>k(C).currentPage=d),"page-size":k(C).pageSize,"onUpdate:pageSize":t[2]||(t[2]=d=>k(C).pageSize=d),"page-sizes":[10,20,50,100],total:k(C).total,layout:"sizes, prev, pager, next, jumper",onSizeChange:me,onCurrentChange:ge,class:"modern-pagination"},null,8,["current-page","page-size","total"])])]),o(nt,{modelValue:_.value,"onUpdate:modelValue":t[3]||(t[3]=d=>_.value=d),onSuccess:F},null,8,["modelValue"]),o(At,{modelValue:M.value,"onUpdate:modelValue":t[4]||(t[4]=d=>M.value=d),"collection-data":V.value},null,8,["modelValue","collection-data"])])}}}),Br=oe(jr,[["__scopeId","data-v-698f84e1"]]);export{Br as default};
