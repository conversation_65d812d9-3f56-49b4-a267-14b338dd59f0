import{c as n,a as e,o as s,r as z,f as R,d as le,h as B,j as d,k as _,m as U,l as t,A as V,n as X,t as r,B as T,F as O,s as te,z as L,L as de,x as ie,_ as ue,e as $e,K as Ce,p as ne,w as Ve,M as Te,N as Se,O as De,E as H,G as Ee,I as ze,C as Me,b as Ie,D as Ne}from"./index-SDZMOyT2.js";import{r as fe}from"./MagnifyingGlassIcon-CMn-3cP9.js";function ae(M,S){return s(),n("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M13.5 4.5 21 12m0 0-7.5 7.5M21 12H3"})])}function ce(M,S){return s(),n("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M19.5 14.25v-2.625a3.375 3.375 0 0 0-3.375-3.375h-1.5A1.125 1.125 0 0 1 13.5 7.125v-1.5a3.375 3.375 0 0 0-3.375-3.375H8.25m0 12.75h7.5m-7.5 3H12M10.5 2.25H5.625c-.621 0-1.125.504-1.125 1.125v17.25c0 .621.504 1.125 1.125 1.125h12.75c.621 0 1.125-.504 1.125-1.125V11.25a9 9 0 0 0-9-9Z"})])}function be(M,S){return s(),n("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M5.25 5.653c0-.856.917-1.398 1.667-.986l11.54 6.347a1.125 1.125 0 0 1 0 1.972l-11.54 6.347a1.125 1.125 0 0 1-1.667-.986V5.653Z"})])}function pe(M,S){return s(),n("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"m7.848 8.25 1.536.887M7.848 8.25a3 3 0 1 1-5.196-3 3 3 0 0 1 5.196 3Zm1.536.887a2.165 2.165 0 0 1 1.083 1.839c.005.351.054.695.14 1.024M9.384 9.137l2.077 1.199M7.848 15.75l1.536-.887m-1.536.887a3 3 0 1 1-5.196 3 3 3 0 0 1 5.196-3Zm1.536-.887a2.165 2.165 0 0 0 1.083-1.838c.005-.352.054-.695.14-1.025m-1.223 2.863 2.077-1.199m0-3.328a4.323 4.323 0 0 1 2.068-1.379l5.325-1.628a4.5 4.5 0 0 1 2.48-.044l.803.215-7.794 4.5m-2.882-1.664A4.33 4.33 0 0 0 10.607 12m3.736 0 7.794 4.5-.802.215a4.5 4.5 0 0 1-2.48-.043l-5.326-1.629a4.324 4.324 0 0 1-2.068-1.379M14.343 12l-2.882 1.664"})])}function me(M,S){return s(),n("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M9.813 15.904 9 18.75l-.813-2.846a4.5 4.5 0 0 0-3.09-3.09L2.25 12l2.846-.813a4.5 4.5 0 0 0 3.09-3.09L9 5.25l.813 2.846a4.5 4.5 0 0 0 3.09 3.09L15.75 12l-2.846.813a4.5 4.5 0 0 0-3.09 3.09ZM18.259 8.715 18 9.75l-.259-1.035a3.375 3.375 0 0 0-2.455-2.456L14.25 6l1.036-.259a3.375 3.375 0 0 0 2.455-2.456L18 2.25l.259 1.035a3.375 3.375 0 0 0 2.456 2.456L21.75 6l-1.035.259a3.375 3.375 0 0 0-2.456 2.456ZM16.894 20.567 16.5 21.75l-.394-1.183a2.25 2.25 0 0 0-1.423-1.423L13.5 18.75l1.183-.394a2.25 2.25 0 0 0 1.423-1.423l.394-1.183.394 1.183a2.25 2.25 0 0 0 1.423 1.423l1.183.394-1.183.394a2.25 2.25 0 0 0-1.423 1.423Z"})])}function ye(M,S){return s(),n("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M5.25 7.5A2.25 2.25 0 0 1 7.5 5.25h9a2.25 2.25 0 0 1 2.25 2.25v9a2.25 2.25 0 0 1-2.25 2.25h-9a2.25 2.25 0 0 1-2.25-2.25v-9Z"})])}const se=z([]),ve=z([]),oe=z(!1),ee=[{id:"WF001",name:"商品采集+智能裁图+批量刊登",apps:[{id:"app1",name:"商品采集",type:"product-collection",settings:{mode:"auto",productSelection:"manual",timeout:30,onError:"stop"}},{id:"app2",name:"智能裁图",type:"smart-crop",settings:{mode:"auto",productSelection:"previous",timeout:20,onError:"skip"}},{id:"app3",name:"批量刊登",type:"batch-listing",settings:{mode:"manual",productSelection:"previous",timeout:60,onError:"stop"}}],usageCount:156,status:"enabled",creator:"张三",createTime:"2024-01-15 10:30:00"},{id:"WF002",name:"一键抠图+超级裂变",apps:[{id:"app1",name:"一键抠图",type:"one-click-cutout",settings:{mode:"auto",productSelection:"manual",timeout:25,onError:"retry"}},{id:"app2",name:"超级裂变",type:"super-split",settings:{mode:"auto",productSelection:"previous",timeout:30,onError:"stop"}}],usageCount:89,status:"enabled",creator:"李四",createTime:"2024-01-14 14:20:00"},{id:"WF003",name:"标题生成+POD合成+批量刊登",apps:[{id:"app1",name:"标题生成",type:"title-generator",settings:{mode:"manual",productSelection:"manual",timeout:15,onError:"stop"}},{id:"app2",name:"POD合成",type:"pod-compose",settings:{mode:"auto",productSelection:"previous",timeout:45,onError:"retry"}},{id:"app3",name:"批量刊登",type:"batch-listing",settings:{mode:"manual",productSelection:"previous",timeout:60,onError:"stop"}}],usageCount:234,status:"disabled",creator:"王五",createTime:"2024-01-13 09:15:00"},{id:"WF004",name:"智能裁图+一键抠图",apps:[{id:"app1",name:"智能裁图",type:"smart-crop",settings:{mode:"auto",productSelection:"manual",timeout:20,onError:"skip"}},{id:"app2",name:"一键抠图",type:"one-click-cutout",settings:{mode:"auto",productSelection:"previous",timeout:25,onError:"retry"}}],usageCount:67,status:"enabled",creator:"赵六",createTime:"2024-01-12 16:45:00"},{id:"WF005",name:"完整电商流程",apps:[{id:"app1",name:"商品采集",type:"product-collection",settings:{mode:"auto",productSelection:"manual",timeout:30,onError:"stop"}},{id:"app2",name:"智能裁图",type:"smart-crop",settings:{mode:"auto",productSelection:"previous",timeout:20,onError:"skip"}},{id:"app3",name:"一键抠图",type:"one-click-cutout",settings:{mode:"auto",productSelection:"previous",timeout:25,onError:"retry"}},{id:"app4",name:"标题生成",type:"title-generator",settings:{mode:"manual",productSelection:"previous",timeout:15,onError:"stop"}},{id:"app5",name:"批量刊登",type:"batch-listing",settings:{mode:"manual",productSelection:"previous",timeout:60,onError:"stop"}}],usageCount:423,status:"enabled",creator:"孙七",createTime:"2024-01-11 11:20:00"}],je=()=>{se.value=[...ee]},We=()=>se.value,Ae=async M=>{oe.value=!0;try{await new Promise(j=>setTimeout(j,1e3));const S={id:`WF${String(se.value.length+1).padStart(3,"0")}`,name:M.name,apps:M.apps,usageCount:0,status:"enabled",creator:"当前用户",createTime:new Date().toLocaleString("zh-CN",{year:"numeric",month:"2-digit",day:"2-digit",hour:"2-digit",minute:"2-digit",second:"2-digit"})};return se.value.unshift(S),S}finally{oe.value=!1}},Le=[{id:"EXE001",workflowId:"WF001",workflowName:"商品采集+智能裁图+批量刊登",workflow:ee[0],status:"completed",stepResults:[{appId:"app1",appName:"商品采集",status:"completed",startTime:"2024-01-15 10:30:00",endTime:"2024-01-15 10:35:00",duration:"5分钟",inputCount:0,outputCount:50},{appId:"app2",appName:"智能裁图",status:"completed",startTime:"2024-01-15 10:35:00",endTime:"2024-01-15 10:40:00",duration:"5分钟",inputCount:50,outputCount:50},{appId:"app3",appName:"批量刊登",status:"completed",startTime:"2024-01-15 10:40:00",endTime:"2024-01-15 10:45:00",duration:"5分钟",inputCount:50,outputCount:48}],startTime:"2024-01-15 10:30:00",endTime:"2024-01-15 10:45:00",duration:"15分钟",executor:"张三"},{id:"EXE002",workflowId:"WF002",workflowName:"一键抠图+超级裂变",workflow:ee[1],status:"running",stepResults:[{appId:"app1",appName:"一键抠图",status:"completed",startTime:"2024-01-15 14:20:00",endTime:"2024-01-15 14:25:00",duration:"5分钟",inputCount:30,outputCount:30},{appId:"app2",appName:"超级裂变",status:"running",startTime:"2024-01-15 14:25:00",inputCount:30,outputCount:0}],startTime:"2024-01-15 14:20:00",duration:"8分钟",executor:"李四"},{id:"EXE003",workflowId:"WF001",workflowName:"商品采集+智能裁图+批量刊登",workflow:ee[0],status:"failed",stepResults:[{appId:"app1",appName:"商品采集",status:"completed",startTime:"2024-01-15 09:00:00",endTime:"2024-01-15 09:05:00",duration:"5分钟",inputCount:0,outputCount:25},{appId:"app2",appName:"智能裁图",status:"failed",startTime:"2024-01-15 09:05:00",endTime:"2024-01-15 09:07:00",duration:"2分钟",inputCount:25,outputCount:0,errorMessage:"图片格式不支持"}],startTime:"2024-01-15 09:00:00",endTime:"2024-01-15 09:07:00",duration:"7分钟",executor:"王五"},{id:"EXE004",workflowId:"WF005",workflowName:"完整电商流程",workflow:ee[4],status:"completed",stepResults:[{appId:"app1",appName:"商品采集",status:"completed",startTime:"2024-01-14 16:00:00",endTime:"2024-01-14 16:10:00",duration:"10分钟",inputCount:0,outputCount:100},{appId:"app2",appName:"智能裁图",status:"completed",startTime:"2024-01-14 16:10:00",endTime:"2024-01-14 16:20:00",duration:"10分钟",inputCount:100,outputCount:100},{appId:"app3",appName:"一键抠图",status:"completed",startTime:"2024-01-14 16:20:00",endTime:"2024-01-14 16:30:00",duration:"10分钟",inputCount:100,outputCount:95},{appId:"app4",appName:"标题生成",status:"completed",startTime:"2024-01-14 16:30:00",endTime:"2024-01-14 16:35:00",duration:"5分钟",inputCount:95,outputCount:95},{appId:"app5",appName:"批量刊登",status:"completed",startTime:"2024-01-14 16:35:00",endTime:"2024-01-14 16:45:00",duration:"10分钟",inputCount:95,outputCount:92}],startTime:"2024-01-14 16:00:00",endTime:"2024-01-14 16:45:00",duration:"45分钟",executor:"孙七"},{id:"EXE005",workflowId:"WF003",workflowName:"标题生成+POD合成+批量刊登",workflow:ee[2],status:"pending",stepResults:[{appId:"app1",appName:"标题生成",status:"pending"},{appId:"app2",appName:"POD合成",status:"pending"},{appId:"app3",appName:"批量刊登",status:"pending"}],startTime:"2024-01-15 15:00:00",executor:"赵六"}],Re=async()=>{oe.value=!0;try{return await new Promise(M=>setTimeout(M,500)),ve.value=[...Le],ve.value}finally{oe.value=!1}};R(()=>se.value),R(()=>ve.value),R(()=>oe.value);const Ue={class:"mb-4"},Be={class:"grid grid-cols-1 md:grid-cols-2 gap-6 max-h-[500px] overflow-y-auto"},Pe=["onClick"],Fe={class:"flex justify-between items-start mb-3"},Oe={class:"text-lg font-semibold text-gray-900 dark:text-dark-text"},Ze={class:"text-sm text-gray-600 dark:text-dark-text-secondary mt-1"},Xe={class:"mb-3"},He={class:"flex items-center space-x-2 overflow-x-auto pb-2"},Ke={class:"flex items-center space-x-1 flex-shrink-0"},Je={class:"w-6 h-6 bg-green-500 rounded-full flex items-center justify-center"},Ge={class:"flex items-center space-x-1 bg-blue-100 dark:bg-blue-900/30 rounded px-2 py-1 flex-shrink-0"},qe={class:"text-xs text-blue-700 dark:text-blue-300"},Qe={class:"flex items-center space-x-1 flex-shrink-0"},Ye={class:"w-6 h-6 bg-red-500 rounded-full flex items-center justify-center"},et={class:"flex justify-between items-center text-sm text-gray-500 dark:text-dark-text-secondary"},tt={key:0,class:"text-center py-12"},at={class:"flex justify-end space-x-3"},st=le({__name:"WorkflowTemplateDialog",props:{modelValue:{type:Boolean}},emits:["update:modelValue","select","create-blank"],setup(M,{emit:S}){const j=M,W=S,E=R({get:()=>j.modelValue,set:N=>W("update:modelValue",N)}),D=z(""),$=R(()=>We()),m=R(()=>D.value?$.value.filter(N=>N.name.toLowerCase().includes(D.value.toLowerCase())||N.apps.some(w=>w.name.toLowerCase().includes(D.value.toLowerCase()))):$.value),I=N=>({"product-collection":ie,"smart-crop":pe,"one-click-cutout":me,"super-split":L,"title-generator":ce,"batch-listing":de,"pod-compose":L})[N]||L,y=N=>{W("select",N),A()},k=()=>{W("create-blank"),A()},A=()=>{D.value="",E.value=!1};return(N,w)=>{const u=_("el-input"),i=_("el-tag"),v=_("el-button"),l=_("el-dialog");return s(),B(l,{modelValue:E.value,"onUpdate:modelValue":w[1]||(w[1]=g=>E.value=g),title:"工作流模板",width:"1000px","close-on-click-modal":!1,class:"template-dialog"},{footer:d(()=>[e("div",at,[t(v,{onClick:A,size:"large"},{default:d(()=>w[5]||(w[5]=[T(" 取消 ")])),_:1,__:[5]}),t(v,{onClick:k,type:"primary",size:"large",plain:""},{default:d(()=>w[6]||(w[6]=[T(" 创建空白工作流 ")])),_:1,__:[6]})])]),default:d(()=>[e("div",Ue,[t(u,{modelValue:D.value,"onUpdate:modelValue":w[0]||(w[0]=g=>D.value=g),placeholder:"搜索模板...",size:"large",clearable:""},{prefix:d(()=>[t(V(fe),{class:"w-5 h-5 text-gray-400"})]),_:1},8,["modelValue"])]),e("div",Be,[(s(!0),n(O,null,X(m.value,g=>(s(),n("div",{key:g.id,class:"bg-gray-50 dark:bg-dark-card rounded-lg p-4 border border-gray-200 dark:border-dark-border hover:border-blue-300 dark:hover:border-blue-600 cursor-pointer transition-all duration-200",onClick:c=>y(g)},[e("div",Fe,[e("div",null,[e("h3",Oe,r(g.name),1),e("p",Ze,r(g.apps.length)+" 个应用 · 使用 "+r(g.usageCount)+" 次 ",1)]),t(i,{type:g.status==="enabled"?"success":"danger",size:"small"},{default:d(()=>[T(r(g.status==="enabled"?"可用":"禁用"),1)]),_:2},1032,["type"])]),e("div",Xe,[e("div",He,[e("div",Ke,[e("div",Je,[t(V(be),{class:"w-3 h-3 text-white"})]),w[2]||(w[2]=e("span",{class:"text-xs text-gray-600 dark:text-dark-text-secondary"},"开始",-1))]),(s(!0),n(O,null,X(g.apps,(c,P)=>(s(),n(O,{key:P},[t(V(ae),{class:"w-3 h-3 text-gray-400 flex-shrink-0"}),e("div",Ge,[(s(),B(te(I(c.type)),{class:"w-3 h-3 text-blue-600 dark:text-blue-400"})),e("span",qe,r(c.name),1)])],64))),128)),t(V(ae),{class:"w-3 h-3 text-gray-400 flex-shrink-0"}),e("div",Qe,[e("div",Ye,[t(V(ye),{class:"w-3 h-3 text-white"})]),w[3]||(w[3]=e("span",{class:"text-xs text-gray-600 dark:text-dark-text-secondary"},"结束",-1))])])]),e("div",et,[e("span",null,"创建者："+r(g.creator),1),e("span",null,r(g.createTime),1)])],8,Pe))),128))]),m.value.length===0?(s(),n("div",tt,[t(V(L),{class:"w-16 h-16 mx-auto text-gray-400 mb-4"}),w[4]||(w[4]=e("p",{class:"text-gray-500 dark:text-dark-text-secondary"},"没有找到匹配的模板",-1))])):U("",!0)]),_:1},8,["modelValue"])}}}),we=ue(st,[["__scopeId","data-v-56b174c9"]]),ot={class:"flex h-[600px]"},lt={class:"w-64 border-r border-gray-200 dark:border-dark-border pr-4"},rt={class:"mb-4"},nt={class:"flex justify-between items-center mb-2"},dt={class:"space-y-2 max-h-[500px] overflow-y-auto"},it=["onDragstart"],ut={class:"flex-1 min-w-0"},ct={class:"text-sm font-medium text-gray-900 dark:text-dark-text truncate"},pt={class:"text-xs text-gray-500 dark:text-dark-text-secondary truncate"},mt={class:"flex-1 px-4"},gt={class:"mb-4"},xt={class:"flex flex-col items-center space-y-6"},kt={class:"flex flex-col items-center space-y-2"},vt={class:"w-16 h-16 bg-green-500 rounded-full flex items-center justify-center shadow-lg"},ft={key:0,class:"flex flex-col items-center space-y-6 w-full"},bt={class:"w-full max-w-2xl"},yt=["onClick"],_t={class:"flex flex-col items-center space-y-2 p-3"},wt={class:"w-14 h-14 bg-blue-500 rounded-full flex items-center justify-center relative shadow-md"},ht=["onClick"],$t={class:"text-xs text-gray-600 dark:text-dark-text-secondary font-medium text-center max-w-20 truncate"},Ct={class:"flex flex-col items-center space-y-2"},Vt={class:"w-16 h-16 bg-red-500 rounded-full flex items-center justify-center shadow-lg"},Tt={key:0,class:"absolute inset-0 flex items-center justify-center"},St={class:"text-center text-gray-500 dark:text-dark-text-secondary"},Dt={class:"w-80 border-l border-gray-200 dark:border-dark-border pl-4"},Et={key:0,class:"space-y-4"},zt={class:"bg-gray-50 dark:bg-dark-card rounded-lg p-4"},Mt={class:"flex items-center space-x-3 mb-4"},It={class:"font-medium text-gray-900 dark:text-dark-text"},Nt={class:"text-sm text-gray-500 dark:text-dark-text-secondary"},jt={class:"space-y-4"},Wt={key:1,class:"text-center text-gray-500 dark:text-dark-text-secondary py-8"},At={class:"flex justify-end space-x-3"},Lt=le({__name:"CreateWorkflowDialog",props:{modelValue:{type:Boolean}},emits:["update:modelValue","success"],setup(M,{emit:S}){const j=M,W=S,E=R({get:()=>j.modelValue,set:x=>W("update:modelValue",x)}),D=z(!1),$=z(""),m=z(null),I=z(!1),y=$e({name:""}),k=z([]),A=[{id:"product-collection",name:"商品采集",type:"product-collection",description:"采集电商平台商品信息"},{id:"smart-crop",name:"智能裁图",type:"smart-crop",description:"智能裁剪商品图片"},{id:"one-click-cutout",name:"一键抠图",type:"one-click-cutout",description:"自动抠图去背景"},{id:"super-split",name:"超级裂变",type:"super-split",description:"图片批量裂变处理"},{id:"title-generator",name:"标题生成",type:"title-generator",description:"智能生成商品标题"},{id:"batch-listing",name:"批量刊登",type:"batch-listing",description:"批量刊登商品到平台"},{id:"pod-compose",name:"POD合成",type:"pod-compose",description:"POD商品合成处理"}],N=R(()=>A.filter(x=>x.name.toLowerCase().includes($.value.toLowerCase())||x.description.toLowerCase().includes($.value.toLowerCase()))),w=R(()=>y.name.trim()&&k.value.length>0),u=x=>({"product-collection":ie,"smart-crop":pe,"one-click-cutout":me,"super-split":L,"title-generator":ce,"batch-listing":de,"pod-compose":L})[x]||L,i=()=>{H.info("跳转到应用市场功能开发中...")},v=x=>{y.name=`${x.name} - 副本`,k.value=x.apps.map(a=>{const Z=A.find(p=>p.type===a.type);return{id:`app_${Date.now()}_${Math.random().toString(36).substring(2,9)}`,name:a.name,type:a.type,description:(Z==null?void 0:Z.description)||"",settings:{...a.settings},datasetConfig:{}}}),I.value=!1,H.success(`已从模板"${x.name}"加载配置`)},l=(x,a)=>{x.dataTransfer&&(x.dataTransfer.setData("application/json",JSON.stringify(a)),x.dataTransfer.effectAllowed="copy")},g=x=>{x.preventDefault(),x.dataTransfer&&(x.dataTransfer.dropEffect="copy")},c=x=>{if(x.preventDefault(),x.dataTransfer)try{const a=JSON.parse(x.dataTransfer.getData("application/json"));P(a)}catch(a){console.error("Failed to parse dropped data:",a)}},P=x=>{const a={...x,id:`${x.id}_${Date.now()}`,settings:{mode:"auto",productSelection:"previous",timeout:30,onError:"stop"},datasetConfig:{}};k.value.push(a),m.value=k.value.length-1},q=x=>{k.value.splice(x,1),m.value===x?m.value=null:m.value!==null&&m.value>x&&m.value--},Q=()=>{m.value=null},C=async()=>{if(w.value){D.value=!0;try{await Ae({name:y.name,apps:k.value}),H.success("工作流创建成功！"),W("success"),h()}catch{H.error("创建失败，请重试")}finally{D.value=!1}}},h=()=>{y.name="",k.value=[],m.value=null,$.value="",I.value=!1,E.value=!1};return(x,a)=>{const Z=_("el-button"),p=_("el-input"),o=_("el-radio"),K=_("el-radio-group"),J=_("el-option"),Y=_("el-select"),ge=_("el-input-number"),F=_("el-dialog");return s(),B(F,{modelValue:E.value,"onUpdate:modelValue":a[10]||(a[10]=f=>E.value=f),title:"新建工作流",width:"1200px","close-on-click-modal":!1,"close-on-press-escape":!1,class:"workflow-dialog"},{footer:d(()=>[e("div",At,[t(Z,{onClick:h,size:"large"},{default:d(()=>a[28]||(a[28]=[T("取消")])),_:1,__:[28]}),t(Z,{onClick:C,type:"primary",size:"large",loading:D.value,disabled:!w.value},{default:d(()=>[T(r(D.value?"创建中...":"确定创建"),1)]),_:1},8,["loading","disabled"])])]),default:d(()=>[e("div",ot,[e("div",lt,[e("div",rt,[e("div",nt,[a[13]||(a[13]=e("h3",{class:"text-lg font-semibold text-gray-900 dark:text-dark-text"},"可用应用",-1)),t(Z,{onClick:i,type:"primary",size:"small",plain:""},{default:d(()=>a[11]||(a[11]=[T(" 应用市场 ")])),_:1,__:[11]}),t(Z,{onClick:a[0]||(a[0]=f=>I.value=!0),size:"small",plain:""},{default:d(()=>a[12]||(a[12]=[T(" 选择模板 ")])),_:1,__:[12]})]),t(p,{modelValue:$.value,"onUpdate:modelValue":a[1]||(a[1]=f=>$.value=f),placeholder:"搜索应用...",size:"small",clearable:""},{prefix:d(()=>[t(V(fe),{class:"w-4 h-4 text-gray-400"})]),_:1},8,["modelValue"])]),e("div",dt,[(s(!0),n(O,null,X(N.value,f=>(s(),n("div",{key:f.id,class:"flex items-center p-3 bg-gray-50 dark:bg-dark-card rounded-lg cursor-pointer hover:bg-gray-100 dark:hover:bg-dark-card/80 transition-colors duration-200",draggable:"true",onDragstart:G=>l(G,f)},[(s(),B(te(u(f.type)),{class:"w-5 h-5 text-blue-600 dark:text-blue-400 mr-3"})),e("div",ut,[e("div",ct,r(f.name),1),e("div",pt,r(f.description),1)]),t(V(Ce),{class:"w-4 h-4 text-gray-400"})],40,it))),128))])]),e("div",mt,[e("div",gt,[a[14]||(a[14]=e("h3",{class:"text-lg font-semibold text-gray-900 dark:text-dark-text mb-2"},"工作流设计",-1)),t(p,{modelValue:y.name,"onUpdate:modelValue":a[2]||(a[2]=f=>y.name=f),placeholder:"请输入工作流名称...",size:"small",class:"mb-2"},null,8,["modelValue"])]),e("div",{class:"bg-white dark:bg-dark-surface rounded-lg p-6 min-h-[450px] border border-gray-200 dark:border-dark-border relative",onDrop:c,onDragover:g},[e("div",xt,[e("div",kt,[e("div",vt,[t(V(be),{class:"w-8 h-8 text-white"})]),a[15]||(a[15]=e("span",{class:"text-sm font-medium text-gray-700 dark:text-dark-text"},"开始",-1))]),k.value.length>0?(s(),n("div",ft,[a[16]||(a[16]=e("div",{class:"w-px h-8 bg-gray-300 dark:bg-gray-600"},null,-1)),e("div",bt,[t(V(Se),{modelValue:k.value,"onUpdate:modelValue":a[3]||(a[3]=f=>k.value=f),onEnd:Q,"item-key":"id",animation:200,handle:".drag-handle","ghost-class":"sortable-ghost","chosen-class":"sortable-chosen","drag-class":"sortable-drag",class:"grid grid-cols-4 gap-4 justify-items-center"},{item:d(({element:f,index:G})=>[e("div",{class:ne(["relative group cursor-pointer drag-handle",{"ring-2 ring-blue-500 rounded-lg":m.value===G}]),onClick:xe=>m.value=G},[e("div",_t,[e("div",wt,[(s(),B(te(u(f.type)),{class:"w-7 h-7 text-white"})),e("button",{onClick:Ve(xe=>q(G),["stop"]),class:"absolute -top-2 -right-2 w-6 h-6 bg-red-500 rounded-full flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity duration-200 shadow-md"},[t(V(Te),{class:"w-3 h-3 text-white"})],8,ht)]),e("span",$t,r(f.name),1)])],10,yt)]),_:1},8,["modelValue"])]),a[17]||(a[17]=e("div",{class:"w-px h-8 bg-gray-300 dark:bg-gray-600"},null,-1))])):U("",!0),e("div",Ct,[e("div",Vt,[t(V(ye),{class:"w-8 h-8 text-white"})]),a[18]||(a[18]=e("span",{class:"text-sm font-medium text-gray-700 dark:text-dark-text"},"结束",-1))])]),k.value.length===0?(s(),n("div",Tt,[e("div",St,[t(V(L),{class:"w-12 h-12 mx-auto mb-2 opacity-50"}),a[19]||(a[19]=e("p",{class:"text-sm"},"拖拽左侧应用到此处开始构建工作流",-1))])])):U("",!0)],32)]),e("div",Dt,[a[27]||(a[27]=e("div",{class:"mb-4"},[e("h3",{class:"text-lg font-semibold text-gray-900 dark:text-dark-text"},"应用设置")],-1)),m.value!==null&&k.value[m.value]?(s(),n("div",Et,[e("div",zt,[e("div",Mt,[(s(),B(te(u(k.value[m.value].type)),{class:"w-6 h-6 text-blue-600 dark:text-blue-400"})),e("div",null,[e("div",It,r(k.value[m.value].name),1),e("div",Nt,r(k.value[m.value].description),1)])]),e("div",jt,[e("div",null,[a[22]||(a[22]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-dark-text mb-2"},"执行模式",-1)),t(K,{modelValue:k.value[m.value].settings.mode,"onUpdate:modelValue":a[4]||(a[4]=f=>k.value[m.value].settings.mode=f),size:"small"},{default:d(()=>[t(o,{label:"auto"},{default:d(()=>a[20]||(a[20]=[T("自动执行")])),_:1,__:[20]}),t(o,{label:"manual"},{default:d(()=>a[21]||(a[21]=[T("手动确认")])),_:1,__:[21]})]),_:1},8,["modelValue"])]),e("div",null,[a[23]||(a[23]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-dark-text mb-2"},"产品选择",-1)),t(Y,{modelValue:k.value[m.value].settings.productSelection,"onUpdate:modelValue":a[5]||(a[5]=f=>k.value[m.value].settings.productSelection=f),placeholder:"选择产品来源",size:"small",class:"w-full"},{default:d(()=>[t(J,{label:"使用上一步结果",value:"previous"}),t(J,{label:"手动选择",value:"manual"}),t(J,{label:"全部产品",value:"all"})]),_:1},8,["modelValue"])]),e("div",null,[a[24]||(a[24]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-dark-text mb-1"},"超时时间（分钟）",-1)),t(ge,{modelValue:k.value[m.value].settings.timeout,"onUpdate:modelValue":a[6]||(a[6]=f=>k.value[m.value].settings.timeout=f),min:1,max:60,size:"small",class:"w-full"},null,8,["modelValue"])]),e("div",null,[a[25]||(a[25]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-dark-text mb-1"},"失败处理",-1)),t(Y,{modelValue:k.value[m.value].settings.onError,"onUpdate:modelValue":a[7]||(a[7]=f=>k.value[m.value].settings.onError=f),size:"small",class:"w-full"},{default:d(()=>[t(J,{label:"停止工作流",value:"stop"}),t(J,{label:"跳过继续",value:"skip"}),t(J,{label:"重试",value:"retry"})]),_:1},8,["modelValue"])])])])])):(s(),n("div",Wt,[t(V(De),{class:"w-12 h-12 mx-auto mb-2 opacity-50"}),a[26]||(a[26]=e("p",{class:"text-sm"},"选择一个应用节点进行设置",-1))]))])]),t(we,{modelValue:I.value,"onUpdate:modelValue":a[8]||(a[8]=f=>I.value=f),onSelect:v,onCreateBlank:a[9]||(a[9]=f=>I.value=!1)},null,8,["modelValue"])]),_:1},8,["modelValue"])}}}),Rt=ue(Lt,[["__scopeId","data-v-7f899664"]]),Ut={key:0,class:"space-y-6"},Bt={class:"bg-gray-50 dark:bg-dark-card rounded-lg p-4"},Pt={class:"grid grid-cols-2 gap-4"},Ft={class:"text-sm text-gray-900 dark:text-dark-text"},Ot={class:"text-sm text-gray-900 dark:text-dark-text"},Zt={class:"text-sm text-gray-900 dark:text-dark-text"},Xt={class:"text-sm text-gray-900 dark:text-dark-text"},Ht={class:"text-sm text-gray-900 dark:text-dark-text"},Kt={class:"bg-white dark:bg-dark-surface rounded-lg border border-gray-200 dark:border-dark-border"},Jt={class:"p-4"},Gt={class:"space-y-4"},qt={class:"flex flex-col items-center"},Qt={class:"flex-1 min-w-0"},Yt={class:"flex items-center justify-between mb-2"},ea={class:"flex items-center space-x-2"},ta={class:"text-base font-medium text-gray-900 dark:text-dark-text"},aa={class:"grid grid-cols-2 gap-4 text-sm"},sa={key:0},oa={class:"text-gray-900 dark:text-dark-text"},la={key:1},ra={class:"text-gray-900 dark:text-dark-text"},na={key:2},da={class:"text-gray-900 dark:text-dark-text"},ia={key:3},ua={class:"text-gray-900 dark:text-dark-text"},ca={key:0,class:"mt-2 p-2 bg-red-50 dark:bg-red-900/20 rounded border border-red-200 dark:border-red-800"},pa={class:"text-sm text-red-700 dark:text-red-300"},ma={class:"flex justify-end"},ga=le({__name:"ExecutionDetailsDialog",props:{modelValue:{type:Boolean},execution:{}},emits:["update:modelValue"],setup(M,{emit:S}){const j=M,W=S,E=R({get:()=>j.modelValue,set:u=>W("update:modelValue",u)}),D=u=>({"product-collection":ie,"smart-crop":pe,"one-click-cutout":me,"super-split":L,"title-generator":ce,"batch-listing":de,"pod-compose":L})[u]||L,$=u=>({商品采集:"product-collection",智能裁图:"smart-crop",一键抠图:"one-click-cutout",超级裂变:"super-split",标题生成:"title-generator",批量刊登:"batch-listing",POD合成:"pod-compose"})[u]||"unknown",m=u=>{switch(u){case"completed":return"success";case"failed":return"danger";case"running":return"warning";case"pending":return"info";default:return"info"}},I=u=>{switch(u){case"completed":return"已完成";case"failed":return"失败";case"running":return"执行中";case"pending":return"等待中";default:return"未知"}},y=u=>m(u),k=u=>I(u),A=u=>{switch(u){case"completed":return"bg-green-500 text-white";case"failed":return"bg-red-500 text-white";case"running":return"bg-blue-500 text-white";case"pending":return"bg-gray-300 dark:bg-gray-600 text-gray-700 dark:text-gray-300";default:return"bg-gray-300 dark:bg-gray-600 text-gray-700 dark:text-gray-300"}},N=u=>{switch(u){case"completed":return"bg-green-300 dark:bg-green-600";case"failed":return"bg-red-300 dark:bg-red-600";case"running":return"bg-blue-300 dark:bg-blue-600";default:return"bg-gray-300 dark:bg-gray-600"}},w=()=>{E.value=!1};return(u,i)=>{const v=_("el-tag"),l=_("el-button"),g=_("el-dialog");return s(),B(g,{modelValue:E.value,"onUpdate:modelValue":i[0]||(i[0]=c=>E.value=c),title:"执行详情",width:"800px","close-on-click-modal":!1,class:"execution-dialog"},{footer:d(()=>[e("div",ma,[t(l,{onClick:w,size:"large"},{default:d(()=>i[14]||(i[14]=[T("关闭")])),_:1,__:[14]})])]),default:d(()=>[u.execution?(s(),n("div",Ut,[e("div",Bt,[i[7]||(i[7]=e("h3",{class:"text-lg font-semibold text-gray-900 dark:text-dark-text mb-3"},"基本信息",-1)),e("div",Pt,[e("div",null,[i[1]||(i[1]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-dark-text mb-1"},"执行ID",-1)),e("p",Ft,r(u.execution.id),1)]),e("div",null,[i[2]||(i[2]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-dark-text mb-1"},"工作流名称",-1)),e("p",Ot,r(u.execution.workflowName),1)]),e("div",null,[i[3]||(i[3]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-dark-text mb-1"},"执行状态",-1)),t(v,{type:m(u.execution.status),size:"small"},{default:d(()=>[T(r(I(u.execution.status)),1)]),_:1},8,["type"])]),e("div",null,[i[4]||(i[4]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-dark-text mb-1"},"执行人",-1)),e("p",Zt,r(u.execution.executor),1)]),e("div",null,[i[5]||(i[5]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-dark-text mb-1"},"开始时间",-1)),e("p",Xt,r(u.execution.startTime),1)]),e("div",null,[i[6]||(i[6]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-dark-text mb-1"},"执行时长",-1)),e("p",Ht,r(u.execution.duration||"进行中"),1)])])]),e("div",Kt,[i[13]||(i[13]=e("h3",{class:"text-lg font-semibold text-gray-900 dark:text-dark-text p-4 border-b border-gray-200 dark:border-dark-border"}," 执行步骤 ",-1)),e("div",Jt,[e("div",Gt,[(s(!0),n(O,null,X(u.execution.stepResults,(c,P)=>(s(),n("div",{key:c.appId,class:"flex items-start space-x-4"},[e("div",qt,[e("div",{class:ne(["w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium",A(c.status)])},r(P+1),3),P<u.execution.stepResults.length-1?(s(),n("div",{key:0,class:ne(["w-px h-12 mt-2",N(c.status)])},null,2)):U("",!0)]),e("div",Qt,[e("div",Yt,[e("div",ea,[(s(),B(te(D($(c.appName))),{class:"w-5 h-5 text-blue-600 dark:text-blue-400"})),e("h4",ta,r(c.appName),1),t(v,{type:y(c.status),size:"small"},{default:d(()=>[T(r(k(c.status)),1)]),_:2},1032,["type"])])]),e("div",aa,[c.startTime?(s(),n("div",sa,[i[8]||(i[8]=e("span",{class:"text-gray-600 dark:text-dark-text-secondary"},"开始时间：",-1)),e("span",oa,r(c.startTime),1)])):U("",!0),c.duration?(s(),n("div",la,[i[9]||(i[9]=e("span",{class:"text-gray-600 dark:text-dark-text-secondary"},"执行时长：",-1)),e("span",ra,r(c.duration),1)])):U("",!0),c.inputCount!==void 0?(s(),n("div",na,[i[10]||(i[10]=e("span",{class:"text-gray-600 dark:text-dark-text-secondary"},"输入数量：",-1)),e("span",da,r(c.inputCount),1)])):U("",!0),c.outputCount!==void 0?(s(),n("div",ia,[i[11]||(i[11]=e("span",{class:"text-gray-600 dark:text-dark-text-secondary"},"输出数量：",-1)),e("span",ua,r(c.outputCount),1)])):U("",!0)]),c.errorMessage?(s(),n("div",ca,[e("p",pa,[i[12]||(i[12]=e("strong",null,"错误信息：",-1)),T(r(c.errorMessage),1)])])):U("",!0)])]))),128))])])])])):U("",!0)]),_:1},8,["modelValue"])}}}),xa=ue(ga,[["__scopeId","data-v-5b52e5e1"]]),ka={key:0,class:"space-y-6"},va={class:"bg-gray-50 dark:bg-dark-card rounded-lg p-4"},fa={class:"grid grid-cols-4 gap-4"},ba={class:"text-center"},ya={class:"text-2xl font-bold text-blue-600 dark:text-blue-400"},_a={class:"text-center"},wa={class:"text-2xl font-bold text-green-600 dark:text-green-400"},ha={class:"text-center"},$a={class:"text-2xl font-bold text-orange-600 dark:text-orange-400"},Ca={class:"text-center"},Va={class:"text-2xl font-bold text-purple-600 dark:text-purple-400"},Ta={class:"bg-white dark:bg-dark-surface rounded-lg border border-gray-200 dark:border-dark-border"},Sa={class:"p-4"},Da={class:"space-y-4"},Ea={class:"grid grid-cols-3 gap-4"},za={class:"bg-blue-50 dark:bg-blue-900/20 rounded-lg p-3 text-center"},Ma={class:"text-lg font-semibold text-blue-600 dark:text-blue-400"},Ia={class:"bg-green-50 dark:bg-green-900/20 rounded-lg p-3 text-center"},Na={class:"text-lg font-semibold text-green-600 dark:text-green-400"},ja={class:"bg-orange-50 dark:bg-orange-900/20 rounded-lg p-3 text-center"},Wa={class:"text-lg font-semibold text-orange-600 dark:text-orange-400"},Aa={class:"bg-gray-50 dark:bg-dark-card rounded-lg p-4"},La={key:0},Ra={class:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4"},Ua=["src","alt"],Ba={class:"text-sm font-medium text-gray-900 dark:text-dark-text truncate"},Pa={class:"text-sm text-green-600 dark:text-green-400 font-semibold"},Fa={key:1},Oa={class:"grid grid-cols-2 md:grid-cols-4 gap-4"},Za=["src","alt"],Xa={class:"text-xs text-gray-600 dark:text-dark-text-secondary text-center"},Ha={key:2},Ka={class:"space-y-2"},Ja={class:"text-sm text-gray-900 dark:text-dark-text"},Ga={class:"text-xs text-gray-500 dark:text-dark-text-secondary mt-1"},qa={key:3},Qa={class:"space-y-2"},Ya={class:"text-sm font-medium text-gray-900 dark:text-dark-text"},es={class:"text-xs text-gray-500 dark:text-dark-text-secondary"},ts={key:4},as={class:"flex justify-between"},ss=le({__name:"ResultsDialog",props:{modelValue:{type:Boolean},execution:{}},emits:["update:modelValue"],setup(M,{emit:S}){const j=M,W=S,E=R({get:()=>j.modelValue,set:v=>W("update:modelValue",v)}),D=z(""),$=R(()=>(j.execution&&j.execution.stepResults.length>0&&(D.value=j.execution.stepResults[0].appId),j.execution)),m=()=>{if(!$.value)return 0;const v=$.value.stepResults[0];return(v==null?void 0:v.inputCount)||0},I=()=>{if(!$.value)return 0;const v=$.value.stepResults[$.value.stepResults.length-1];return(v==null?void 0:v.outputCount)||0},y=()=>{const v=m(),l=I();return v===0?0:Math.round(l/v*100)},k=v=>Array.from({length:Math.min(v,6)},(l,g)=>({id:`product_${g+1}`,title:`商品标题 ${g+1} - 高质量产品描述`,price:(Math.random()*100+10).toFixed(2),image:`https://picsum.photos/200/200?random=${g+1}`})),A=v=>Array.from({length:Math.min(v,8)},(l,g)=>({id:`image_${g+1}`,name:`处理后图片_${g+1}.jpg`,image:`https://picsum.photos/150/150?random=${g+10}`})),N=v=>{const l=["高品质时尚T恤 - 舒适透气 多色可选 男女通用款式","精美陶瓷马克杯 - 创意设计 办公室必备 礼品首选","多功能手机壳 - 防摔保护 时尚外观 适配多机型","舒适运动鞋 - 轻便透气 专业运动 日常休闲两用","优质帆布包 - 大容量设计 环保材质 时尚百搭"];return Array.from({length:Math.min(v,5)},(g,c)=>({id:`title_${c+1}`,title:l[c%l.length],length:l[c%l.length].length}))},w=v=>{const l=["Amazon","eBay","Shopify","Etsy"];return Array.from({length:Math.min(v,8)},(g,c)=>({id:`listing_${c+1}`,platform:l[c%l.length],productId:`PRD${String(c+1).padStart(6,"0")}`,status:Math.random()>.2?"success":"failed"}))},u=()=>{H.success("结果下载功能开发中...")},i=()=>{E.value=!1};return(v,l)=>{const g=_("el-tag"),c=_("el-tab-pane"),P=_("el-tabs"),q=_("el-button"),Q=_("el-dialog");return s(),B(Q,{modelValue:E.value,"onUpdate:modelValue":l[1]||(l[1]=C=>E.value=C),title:"处理结果",width:"1000px","close-on-click-modal":!1,class:"results-dialog"},{footer:d(()=>[e("div",as,[t(q,{onClick:u,type:"primary",plain:""},{default:d(()=>l[13]||(l[13]=[T(" 下载结果 ")])),_:1,__:[13]}),t(q,{onClick:i,size:"large"},{default:d(()=>l[14]||(l[14]=[T("关闭")])),_:1,__:[14]})])]),default:d(()=>[$.value?(s(),n("div",ka,[e("div",va,[l[6]||(l[6]=e("h3",{class:"text-lg font-semibold text-gray-900 dark:text-dark-text mb-3"},"处理概览",-1)),e("div",fa,[e("div",ba,[e("div",ya,r(m()),1),l[2]||(l[2]=e("div",{class:"text-sm text-gray-600 dark:text-dark-text-secondary"},"总输入",-1))]),e("div",_a,[e("div",wa,r(I()),1),l[3]||(l[3]=e("div",{class:"text-sm text-gray-600 dark:text-dark-text-secondary"},"总输出",-1))]),e("div",ha,[e("div",$a,r(y())+"%",1),l[4]||(l[4]=e("div",{class:"text-sm text-gray-600 dark:text-dark-text-secondary"},"成功率",-1))]),e("div",Ca,[e("div",Va,r($.value.duration||"0分钟"),1),l[5]||(l[5]=e("div",{class:"text-sm text-gray-600 dark:text-dark-text-secondary"},"总耗时",-1))])])]),e("div",Ta,[l[12]||(l[12]=e("div",{class:"p-4 border-b border-gray-200 dark:border-dark-border"},[e("h3",{class:"text-lg font-semibold text-gray-900 dark:text-dark-text"},"各步骤处理结果")],-1)),e("div",Sa,[t(P,{modelValue:D.value,"onUpdate:modelValue":l[0]||(l[0]=C=>D.value=C),type:"border-card"},{default:d(()=>[(s(!0),n(O,null,X($.value.stepResults,C=>(s(),B(c,{key:C.appId,label:C.appName,name:C.appId},{default:d(()=>[e("div",Da,[e("div",Ea,[e("div",za,[e("div",Ma,r(C.inputCount||0),1),l[7]||(l[7]=e("div",{class:"text-sm text-gray-600 dark:text-dark-text-secondary"},"输入数量",-1))]),e("div",Ia,[e("div",Na,r(C.outputCount||0),1),l[8]||(l[8]=e("div",{class:"text-sm text-gray-600 dark:text-dark-text-secondary"},"输出数量",-1))]),e("div",ja,[e("div",Wa,r(C.duration||"0分钟"),1),l[9]||(l[9]=e("div",{class:"text-sm text-gray-600 dark:text-dark-text-secondary"},"处理时长",-1))])]),e("div",Aa,[l[11]||(l[11]=e("h4",{class:"text-base font-medium text-gray-900 dark:text-dark-text mb-3"},"处理结果数据",-1)),C.appName==="商品采集"?(s(),n("div",La,[e("div",Ra,[(s(!0),n(O,null,X(k(C.outputCount||0),h=>(s(),n("div",{key:h.id,class:"bg-white dark:bg-dark-surface rounded-lg p-3 border border-gray-200 dark:border-dark-border"},[e("img",{src:h.image,alt:h.title,class:"w-full h-32 object-cover rounded mb-2"},null,8,Ua),e("h5",Ba,r(h.title),1),e("p",Pa,"$"+r(h.price),1)]))),128))])])):C.appName==="智能裁图"||C.appName==="一键抠图"?(s(),n("div",Fa,[e("div",Oa,[(s(!0),n(O,null,X(A(C.outputCount||0),h=>(s(),n("div",{key:h.id,class:"bg-white dark:bg-dark-surface rounded-lg p-2 border border-gray-200 dark:border-dark-border"},[e("img",{src:h.image,alt:h.name,class:"w-full h-24 object-cover rounded mb-1"},null,8,Za),e("p",Xa,r(h.name),1)]))),128))])])):C.appName==="标题生成"?(s(),n("div",Ha,[e("div",Ka,[(s(!0),n(O,null,X(N(C.outputCount||0),h=>(s(),n("div",{key:h.id,class:"bg-white dark:bg-dark-surface rounded-lg p-3 border border-gray-200 dark:border-dark-border"},[e("p",Ja,r(h.title),1),e("p",Ga,"长度: "+r(h.length)+" 字符",1)]))),128))])])):C.appName==="批量刊登"?(s(),n("div",qa,[e("div",Qa,[(s(!0),n(O,null,X(w(C.outputCount||0),h=>(s(),n("div",{key:h.id,class:"bg-white dark:bg-dark-surface rounded-lg p-3 border border-gray-200 dark:border-dark-border flex justify-between items-center"},[e("div",null,[e("p",Ya,r(h.platform),1),e("p",es,r(h.productId),1)]),t(g,{type:h.status==="success"?"success":"danger",size:"small"},{default:d(()=>[T(r(h.status==="success"?"成功":"失败"),1)]),_:2},1032,["type"])]))),128))])])):(s(),n("div",ts,l[10]||(l[10]=[e("p",{class:"text-gray-500 dark:text-dark-text-secondary text-center py-4"}," 暂无结果数据展示 ",-1)])))])])]),_:2},1032,["label","name"]))),128))]),_:1},8,["modelValue"])])])])):U("",!0)]),_:1},8,["modelValue"])}}}),os=ue(ss,[["__scopeId","data-v-392a9730"]]),ls={class:"p-6 bg-gray-50 dark:bg-dark-background min-h-screen"},rs={class:"flex justify-between items-center mb-6"},ns={class:"flex space-x-3"},ds={class:"bg-white dark:bg-dark-surface rounded-xl shadow-elegant dark:shadow-elegant-dark border border-gray-100 dark:border-dark-border p-4 mb-6"},is={class:"flex justify-between items-center"},us={class:"flex space-x-4"},cs={class:"flex items-center space-x-4"},ps={class:"text-sm text-gray-600 dark:text-dark-text-secondary"},ms={key:0,class:"flex items-center space-x-2"},gs={class:"text-sm text-blue-600 dark:text-blue-400"},xs={class:"bg-white dark:bg-dark-surface rounded-xl shadow-elegant dark:shadow-elegant-dark border border-gray-100 dark:border-dark-border overflow-hidden"},ks={class:"flex items-center space-x-2"},vs={class:"flex items-center bg-gray-50 dark:bg-dark-card rounded-lg px-3 py-2 space-x-2 min-w-0 flex-1"},fs={class:"flex items-center space-x-1 flex-shrink-0"},bs={class:"w-6 h-6 bg-green-500 rounded-full flex items-center justify-center"},ys={class:"flex items-center space-x-1 overflow-x-auto"},_s={class:"text-xs"},ws={class:"flex items-center space-x-1 flex-shrink-0"},hs={class:"w-6 h-6 bg-red-500 rounded-full flex items-center justify-center"},$s={class:"flex justify-center space-x-1"},Cs={class:"px-6 py-4 border-t border-gray-100 dark:border-dark-border"},Ss=le({__name:"index",setup(M){const S=z(""),j=z(""),W=z([]),E=z(!1),D=z(!1),$=z(!1),m=z(!1),I=z(null),y=z({currentPage:1,pageSize:20,total:0}),k=z([]),A=z(!1),N=R(()=>{let p=k.value;return S.value&&(p=p.filter(o=>o.workflowName.toLowerCase().includes(S.value.toLowerCase())||o.id.toLowerCase().includes(S.value.toLowerCase()))),j.value&&(p=p.filter(o=>o.status===j.value)),p}),w=R(()=>{const p=(y.value.currentPage-1)*y.value.pageSize,o=p+y.value.pageSize;return N.value.slice(p,o)}),u=p=>({"product-collection":ie,"smart-crop":pe,"one-click-cutout":me,"super-split":L,"title-generator":ce,"batch-listing":de,"pod-compose":L})[p]||L,i=p=>{switch(p){case"completed":return"bg-green-100 dark:bg-green-900/30 text-green-700 dark:text-green-300";case"failed":return"bg-red-100 dark:bg-red-900/30 text-red-700 dark:text-red-300";case"running":return"bg-blue-100 dark:bg-blue-900/30 text-blue-700 dark:text-blue-300";case"pending":return"bg-gray-100 dark:bg-gray-900/30 text-gray-700 dark:text-gray-300";default:return"bg-gray-100 dark:bg-gray-900/30 text-gray-700 dark:text-gray-300"}},v=p=>{switch(p){case"completed":return"success";case"failed":return"danger";case"running":return"warning";case"pending":return"info";default:return"info"}},l=p=>{switch(p){case"completed":return"已完成";case"failed":return"失败";case"running":return"执行中";case"pending":return"等待中";default:return"未知"}},g=async()=>{A.value=!0;try{const p=await Re();k.value=p,y.value.total=N.value.length}catch{H.error("加载执行历史失败")}finally{A.value=!1}},c=p=>{W.value=p},P=()=>{y.value.currentPage=1,y.value.total=N.value.length},q=p=>{I.value=p,$.value=!0},Q=p=>{I.value=p,m.value=!0},C=p=>{H.success(`正在从模板"${p.name}"创建工作流...`),D.value=!1,E.value=!0},h=()=>{H.success("导出执行历史功能开发中...")},x=()=>{H.success("操作成功！"),g()},a=p=>{y.value.pageSize=p,y.value.currentPage=1,g()},Z=p=>{y.value.currentPage=p,g()};return Ee(()=>{je(),g()}),(p,o)=>{const K=_("el-button"),J=_("el-input"),Y=_("el-option"),ge=_("el-select"),F=_("el-table-column"),f=_("el-tag"),G=_("el-table"),xe=_("el-pagination"),he=Ne("loading");return s(),n("div",ls,[e("div",rs,[o[15]||(o[15]=e("div",null,[e("h1",{class:"text-2xl font-bold text-gray-900 dark:text-dark-text mb-2"},"工作流"),e("p",{class:"text-gray-600 dark:text-dark-text-secondary"},"查看工作流执行历史和管理工作流")],-1)),e("div",ns,[t(K,{onClick:o[0]||(o[0]=b=>E.value=!0),type:"primary",size:"large",icon:V(ze)},{default:d(()=>o[12]||(o[12]=[T(" 新建工作流 ")])),_:1,__:[12]},8,["icon"]),t(K,{onClick:o[1]||(o[1]=b=>D.value=!0),size:"large"},{default:d(()=>o[13]||(o[13]=[T(" 工作流模板 ")])),_:1,__:[13]}),t(K,{onClick:h,size:"large",icon:V(Me)},{default:d(()=>o[14]||(o[14]=[T(" 导出 ")])),_:1,__:[14]},8,["icon"])])]),e("div",ds,[e("div",is,[e("div",us,[t(J,{modelValue:S.value,"onUpdate:modelValue":o[2]||(o[2]=b=>S.value=b),placeholder:"搜索工作流名称...",style:{width:"300px"},clearable:"",onInput:P},{prefix:d(()=>[t(V(fe),{class:"w-4 h-4 text-gray-400"})]),_:1},8,["modelValue"]),t(ge,{modelValue:j.value,"onUpdate:modelValue":o[3]||(o[3]=b=>j.value=b),placeholder:"状态筛选",style:{width:"120px"},clearable:"",onChange:P},{default:d(()=>[t(Y,{label:"全部",value:""}),t(Y,{label:"启用",value:"enabled"}),t(Y,{label:"禁用",value:"disabled"})]),_:1},8,["modelValue"])]),e("div",cs,[e("div",ps," 共 "+r(y.value.total)+" 条执行记录 ",1),W.value.length>0?(s(),n("div",ms,[e("span",gs,"已选择 "+r(W.value.length)+" 项",1),t(K,{onClick:o[4]||(o[4]=()=>V(H).info("批量操作功能开发中...")),type:"primary",size:"small",plain:""},{default:d(()=>o[16]||(o[16]=[T(" 批量启用/禁用 ")])),_:1,__:[16]})])):U("",!0)])])]),e("div",xs,[Ie((s(),B(G,{data:w.value,onSelectionChange:c,class:"w-full","header-cell-style":{backgroundColor:"#f8fafc",color:"#374151",fontWeight:"600"}},{default:d(()=>[t(F,{type:"selection",width:"55"}),t(F,{prop:"id",label:"执行ID",width:"120"}),t(F,{prop:"workflowName",label:"工作流名称","min-width":"200"}),t(F,{label:"工作流程","min-width":"300"},{default:d(({row:b})=>[e("div",ks,[e("div",vs,[e("div",fs,[e("div",bs,[t(V(be),{class:"w-3 h-3 text-white"})]),o[17]||(o[17]=e("span",{class:"text-xs text-gray-600 dark:text-dark-text-secondary"},"开始",-1))]),t(V(ae),{class:"w-4 h-4 text-gray-400 flex-shrink-0"}),e("div",ys,[(s(!0),n(O,null,X(b.workflow.apps,(re,ke)=>{var _e;return s(),n("div",{key:ke,class:"flex items-center space-x-1 flex-shrink-0"},[e("div",{class:ne(["flex items-center space-x-1 rounded px-2 py-1",i((_e=b.stepResults[ke])==null?void 0:_e.status)])},[(s(),B(te(u(re.type)),{class:"w-4 h-4"})),e("span",_s,r(re.name),1)],2),ke<b.workflow.apps.length-1?(s(),B(V(ae),{key:0,class:"w-3 h-3 text-gray-400"})):U("",!0)])}),128))]),t(V(ae),{class:"w-4 h-4 text-gray-400 flex-shrink-0"}),e("div",ws,[e("div",hs,[t(V(ye),{class:"w-3 h-3 text-white"})]),o[18]||(o[18]=e("span",{class:"text-xs text-gray-600 dark:text-dark-text-secondary"},"结束",-1))])])])]),_:1}),t(F,{label:"执行状态",width:"120",align:"center"},{default:d(({row:b})=>[t(f,{type:v(b.status),size:"small"},{default:d(()=>[T(r(l(b.status)),1)]),_:2},1032,["type"])]),_:1}),t(F,{prop:"duration",label:"执行时长",width:"120",align:"center"}),t(F,{prop:"executor",label:"执行人",width:"120"}),t(F,{prop:"startTime",label:"开始时间",width:"180"}),t(F,{label:"操作",width:"150",align:"center"},{default:d(({row:b})=>[e("div",$s,[t(K,{onClick:re=>q(b),type:"primary",size:"small",plain:""},{default:d(()=>o[19]||(o[19]=[T(" 查看详情 ")])),_:2,__:[19]},1032,["onClick"]),t(K,{onClick:re=>Q(b),type:"success",size:"small",plain:"",disabled:b.status!=="completed"},{default:d(()=>o[20]||(o[20]=[T(" 处理结果 ")])),_:2,__:[20]},1032,["onClick","disabled"])])]),_:1})]),_:1},8,["data"])),[[he,A.value]]),e("div",Cs,[t(xe,{"current-page":y.value.currentPage,"onUpdate:currentPage":o[5]||(o[5]=b=>y.value.currentPage=b),"page-size":y.value.pageSize,"onUpdate:pageSize":o[6]||(o[6]=b=>y.value.pageSize=b),total:y.value.total,"page-sizes":[10,20,50,100],layout:"total, sizes, prev, pager, next, jumper",onSizeChange:a,onCurrentChange:Z},null,8,["current-page","page-size","total"])])]),t(Rt,{modelValue:E.value,"onUpdate:modelValue":o[7]||(o[7]=b=>E.value=b),onSuccess:x},null,8,["modelValue"]),t(we,{modelValue:D.value,"onUpdate:modelValue":o[8]||(o[8]=b=>D.value=b),onSelect:C,onCreateBlank:o[9]||(o[9]=b=>E.value=!0)},null,8,["modelValue"]),t(xa,{modelValue:$.value,"onUpdate:modelValue":o[10]||(o[10]=b=>$.value=b),execution:I.value,onViewResults:Q},null,8,["modelValue","execution"]),t(os,{modelValue:m.value,"onUpdate:modelValue":o[11]||(o[11]=b=>m.value=b),execution:I.value},null,8,["modelValue","execution"])])}}});export{Ss as default};
