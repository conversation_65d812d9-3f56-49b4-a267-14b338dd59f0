import{d as O,f as I,r as c,h as K,j as n,k as x,c as V,m as D,a as e,t as s,l as d,B as h,p as L,E as _,o as v,_ as H,G as Q,H as X,b as U,v as Y,D as Z,F as ee}from"./index-g0Lcbgij.js";const te={class:"flex items-center justify-between p-6 border-b border-gray-100 dark:border-dark-border"},re={class:"flex items-center space-x-3"},oe={class:"text-sm text-gray-600 dark:text-dark-text-secondary"},se={key:0,class:"p-6 grid grid-cols-2 md:grid-cols-4 gap-4"},ae={class:"bg-gradient-to-br from-purple-50 to-purple-100 dark:from-purple-900/20 dark:to-purple-800/20 p-4 rounded-xl border border-purple-200 dark:border-purple-800"},de={class:"flex items-center space-x-2"},le={class:"text-sm font-bold text-purple-900 dark:text-purple-100"},ne={class:"bg-gradient-to-br from-blue-50 to-blue-100 dark:from-blue-900/20 dark:to-blue-800/20 p-4 rounded-xl border border-blue-200 dark:border-blue-800"},ie={class:"flex items-center space-x-2"},ue={class:"text-sm font-bold text-blue-900 dark:text-blue-100"},ce={class:"bg-gradient-to-br from-emerald-50 to-emerald-100 dark:from-emerald-900/20 dark:to-emerald-800/20 p-4 rounded-xl border border-emerald-200 dark:border-emerald-800"},pe={class:"flex items-center space-x-2"},xe={class:"text-sm font-bold text-emerald-900 dark:text-emerald-100"},be={class:"bg-gradient-to-br from-amber-50 to-amber-100 dark:from-amber-900/20 dark:to-amber-800/20 p-4 rounded-xl border border-amber-200 dark:border-amber-800"},ge={class:"flex items-center space-x-2"},me={class:"text-sm font-bold text-amber-900 dark:text-amber-100"},ke={key:1,class:"px-6 pb-6 space-y-6"},he={class:"flex items-center justify-between"},ve={class:"flex space-x-3"},fe={class:"grid grid-cols-1 md:grid-cols-2 gap-6"},we={class:"bg-white dark:bg-dark-surface rounded-xl border border-gray-200 dark:border-dark-border overflow-hidden"},ye={class:"p-4"},_e={class:"text-lg font-bold text-gray-900 dark:text-dark-text mt-4"},Pe={class:"bg-white dark:bg-dark-surface rounded-xl border border-gray-200 dark:border-dark-border overflow-hidden"},Ce={class:"p-4"},je={class:"grid grid-cols-2 gap-4"},Se={class:"flex items-center space-x-3"},$e={class:"text-sm font-medium text-gray-900 dark:text-dark-text"},Ve={class:"mt-4 pt-4 border-t border-gray-100 dark:border-dark-border"},Me={class:"grid grid-cols-2 gap-4"},ze={class:"text-sm font-medium text-gray-900 dark:text-dark-text"},Be={class:"bg-white dark:bg-dark-surface rounded-xl border border-gray-200 dark:border-dark-border overflow-hidden"},De={class:"overflow-x-auto"},Te={class:"flex justify-center"},Ue={class:"font-mono text-sm text-gray-600 dark:text-dark-text-secondary"},Oe={class:"text-sm font-medium text-gray-900 dark:text-dark-text"},Ie={class:"font-medium text-green-600 dark:text-green-400"},Ke={class:"font-medium text-gray-900 dark:text-dark-text"},Le={class:"flex items-center justify-between p-6 border-t border-gray-100 dark:border-dark-border bg-gray-50 dark:bg-dark-card/50"},He={class:"text-sm text-gray-600 dark:text-dark-text-secondary"},Ne=O({__name:"ViewPodProductDialog",props:{modelValue:{type:Boolean},product:{}},emits:["update:modelValue"],setup(T,{emit:f}){const w=T,P=f,b=I({get:()=>w.modelValue,set:a=>P("update:modelValue",a)}),g=c([{id:"SKU001",name:"S码 - 白色",price:79.9,stock:100,image:"https://picsum.photos/100/100?random=301"},{id:"SKU002",name:"M码 - 白色",price:79.9,stock:80,image:"https://picsum.photos/100/100?random=302"},{id:"SKU003",name:"L码 - 白色",price:79.9,stock:90,image:"https://picsum.photos/100/100?random=303"},{id:"SKU004",name:"S码 - 黑色",price:79.9,stock:70,image:"https://picsum.photos/100/100?random=304"},{id:"SKU005",name:"M码 - 黑色",price:79.9,stock:85,image:"https://picsum.photos/100/100?random=305"},{id:"SKU006",name:"L码 - 黑色",price:79.9,stock:65,image:"https://picsum.photos/100/100?random=306"}]),y=()=>{b.value=!1},M=a=>{const t={published:"bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-300",publishing:"bg-yellow-100 dark:bg-yellow-900/30 text-yellow-800 dark:text-yellow-300",unpublished:"bg-gray-100 dark:bg-gray-900/30 text-gray-800 dark:text-gray-300"};return t[a]||t.unpublished},C=a=>({published:"已刊登",publishing:"刊登中",unpublished:"未刊登"})[a]||"未知",j=()=>{_.success("正在刊登POD商品...")},u=()=>{_.success("正在下载商品图片...")};return(a,t)=>{const m=x("el-button"),S=x("el-image"),k=x("el-table-column"),z=x("el-table"),B=x("el-dialog");return v(),K(B,{modelValue:b.value,"onUpdate:modelValue":t[0]||(t[0]=l=>b.value=l),width:"1200px","before-close":y,"show-close":!1,class:"modern-dialog"},{header:n(()=>{var l;return[e("div",te,[e("div",re,[t[2]||(t[2]=e("div",{class:"w-10 h-10 bg-gradient-to-br from-purple-500 to-purple-600 rounded-xl flex items-center justify-center"},[e("svg",{class:"w-6 h-6 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M7 4V2a1 1 0 011-1h8a1 1 0 011 1v2m-9 0h10m-10 0a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V6a2 2 0 00-2-2"})])],-1)),e("div",null,[t[1]||(t[1]=e("h3",{class:"text-xl font-bold text-gray-900 dark:text-dark-text"},"POD商品详情",-1)),e("p",oe,"ID: "+s(((l=a.product)==null?void 0:l.id)||""),1)])]),e("button",{onClick:y,class:"p-2 text-gray-400 hover:text-gray-600 dark:text-dark-text-secondary dark:hover:text-dark-text rounded-lg hover:bg-gray-100 dark:hover:bg-dark-card transition-all duration-200"},t[3]||(t[3]=[e("svg",{class:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M6 18L18 6M6 6l12 12"})],-1)]))])]}),footer:n(()=>{var l;return[e("div",Le,[e("div",He,s(((l=a.product)==null?void 0:l.skuCount)||0)+" 个SKU规格 ",1),e("div",{class:"flex items-center space-x-3"},[e("button",{onClick:y,class:"px-6 py-2.5 text-gray-700 dark:text-dark-text font-medium rounded-lg border border-gray-300 dark:border-dark-border hover:bg-gray-50 dark:hover:bg-dark-border transition-all duration-200"}," 关闭 "),e("button",{onClick:j,class:"inline-flex items-center px-6 py-2.5 bg-gradient-to-r from-purple-500 to-purple-600 hover:from-purple-600 hover:to-purple-700 text-white font-medium rounded-lg shadow-lg hover:shadow-xl transition-all duration-200 transform hover:scale-105"},t[23]||(t[23]=[e("svg",{class:"w-5 h-5 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"})],-1),h(" 刊登商品 ")]))])])]}),default:n(()=>[a.product?(v(),V("div",se,[e("div",ae,[e("div",de,[t[5]||(t[5]=e("div",{class:"w-8 h-8 bg-purple-500 rounded-lg flex items-center justify-center"},[e("svg",{class:"w-4 h-4 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"})])],-1)),e("div",null,[t[4]||(t[4]=e("p",{class:"text-xs text-purple-600 dark:text-purple-400 font-medium"},"刊登状态",-1)),e("p",le,s(C(a.product.publishStatus)),1)])])]),e("div",ne,[e("div",ie,[t[7]||(t[7]=e("div",{class:"w-8 h-8 bg-blue-500 rounded-lg flex items-center justify-center"},[e("svg",{class:"w-4 h-4 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"})])],-1)),e("div",null,[t[6]||(t[6]=e("p",{class:"text-xs text-blue-600 dark:text-blue-400 font-medium"},"SKU数量",-1)),e("p",ue,s(a.product.skuCount),1)])])]),e("div",ce,[e("div",pe,[t[9]||(t[9]=e("div",{class:"w-8 h-8 bg-emerald-500 rounded-lg flex items-center justify-center"},[e("svg",{class:"w-4 h-4 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z"})])],-1)),e("div",null,[t[8]||(t[8]=e("p",{class:"text-xs text-emerald-600 dark:text-emerald-400 font-medium"},"商品价格",-1)),e("p",xe,"¥"+s(a.product.minPrice.toFixed(2)),1)])])]),e("div",be,[e("div",ge,[t[11]||(t[11]=e("div",{class:"w-8 h-8 bg-amber-500 rounded-lg flex items-center justify-center"},[e("svg",{class:"w-4 h-4 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"})])],-1)),e("div",null,[t[10]||(t[10]=e("p",{class:"text-xs text-amber-600 dark:text-amber-400 font-medium"},"创建人",-1)),e("p",me,s(a.product.creator),1)])])])])):D("",!0),a.product?(v(),V("div",ke,[e("div",he,[t[14]||(t[14]=e("h3",{class:"text-lg font-semibold text-gray-900 dark:text-dark-text"},"商品详情",-1)),e("div",ve,[d(m,{type:"primary",size:"small",onClick:j},{default:n(()=>t[12]||(t[12]=[e("svg",{class:"w-4 h-4 mr-1",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"})],-1),h(" 刊登商品 ")])),_:1,__:[12]}),d(m,{size:"small",onClick:u},{default:n(()=>t[13]||(t[13]=[e("svg",{class:"w-4 h-4 mr-1",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4"})],-1),h(" 下载图片 ")])),_:1,__:[13]})])]),e("div",fe,[e("div",we,[t[15]||(t[15]=e("div",{class:"p-4 border-b border-gray-100 dark:border-dark-border"},[e("h4",{class:"text-sm font-medium text-gray-900 dark:text-dark-text"},"商品图片")],-1)),e("div",ye,[d(S,{src:a.product.coverImage,"preview-src-list":[a.product.coverImage],fit:"cover",class:"w-full h-64 object-cover rounded-lg border border-gray-200 dark:border-dark-border","preview-teleported":!0},null,8,["src","preview-src-list"]),e("h2",_e,s(a.product.name),1)])]),e("div",Pe,[t[21]||(t[21]=e("div",{class:"p-4 border-b border-gray-100 dark:border-dark-border"},[e("h4",{class:"text-sm font-medium text-gray-900 dark:text-dark-text"},"合成信息")],-1)),e("div",Ce,[e("div",je,[e("div",Se,[t[17]||(t[17]=e("img",{src:"https://picsum.photos/100/100?random=201",alt:"基础商品",class:"w-16 h-16 rounded-lg object-cover"},null,-1)),e("div",null,[t[16]||(t[16]=e("p",{class:"text-xs text-gray-500 dark:text-dark-text-secondary"},"基础商品",-1)),e("p",$e,s(a.product.baseProduct),1)])]),t[18]||(t[18]=e("div",{class:"flex items-center space-x-3"},[e("img",{src:"https://picsum.photos/100/100?random=202",alt:"印刷图案",class:"w-16 h-16 rounded-lg object-cover"}),e("div",null,[e("p",{class:"text-xs text-gray-500 dark:text-dark-text-secondary"},"印刷图案"),e("p",{class:"text-sm font-medium text-gray-900 dark:text-dark-text"},"自定义图案")])],-1))]),e("div",Ve,[e("div",Me,[e("div",null,[t[19]||(t[19]=e("p",{class:"text-xs text-gray-500 dark:text-dark-text-secondary"},"创建时间",-1)),e("p",ze,s(a.product.createTime),1)]),e("div",null,[t[20]||(t[20]=e("p",{class:"text-xs text-gray-500 dark:text-dark-text-secondary"},"刊登状态",-1)),e("span",{class:L([M(a.product.publishStatus),"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium"])},s(C(a.product.publishStatus)),3)])])])])])]),e("div",Be,[t[22]||(t[22]=e("div",{class:"p-4 border-b border-gray-100 dark:border-dark-border"},[e("h4",{class:"text-sm font-medium text-gray-900 dark:text-dark-text"},"SKU信息")],-1)),e("div",De,[d(z,{data:g.value,style:{width:"100%"},"max-height":"400",class:"modern-table","header-cell-style":{backgroundColor:"var(--el-bg-color-page)",color:"var(--el-text-color-primary)",fontWeight:"600",borderBottom:"1px solid var(--el-border-color-light)"},"row-style":{backgroundColor:"transparent"}},{default:n(()=>[d(k,{label:"SKU图片",width:"100"},{default:n(l=>[e("div",Te,[d(S,{src:l.row.image,"preview-src-list":[l.row.image],fit:"cover",class:"w-12 h-12 rounded-lg border border-gray-200 dark:border-dark-border","preview-teleported":!0},null,8,["src","preview-src-list"])])]),_:1}),d(k,{prop:"id",label:"SKU ID",width:"120"},{default:n(l=>[e("span",Ue,s(l.row.id),1)]),_:1}),d(k,{prop:"name",label:"规格","min-width":"150"},{default:n(l=>[e("span",Oe,s(l.row.name),1)]),_:1}),d(k,{prop:"price",label:"价格",width:"100"},{default:n(l=>[e("span",Ie,"¥"+s(l.row.price),1)]),_:1}),d(k,{prop:"stock",label:"库存",width:"100"},{default:n(l=>[e("span",Ke,s(l.row.stock),1)]),_:1})]),_:1},8,["data"])])])])):D("",!0)]),_:1},8,["modelValue"])}}}),Ee=H(Ne,[["__scopeId","data-v-93b48c28"]]),Fe={class:"space-y-6"},We={class:"grid grid-cols-1 md:grid-cols-4 gap-6"},Ae={class:"bg-white dark:bg-dark-surface rounded-xl p-6 shadow-elegant dark:shadow-elegant-dark border border-gray-100 dark:border-dark-border hover:shadow-lg dark:hover:shadow-2xl transition-all duration-300"},Ge={class:"flex items-center justify-between"},Re={class:"text-2xl font-bold text-gray-900 dark:text-dark-text"},qe={class:"bg-white dark:bg-dark-surface rounded-xl p-6 shadow-elegant dark:shadow-elegant-dark border border-gray-100 dark:border-dark-border hover:shadow-lg dark:hover:shadow-2xl transition-all duration-300"},Je={class:"flex items-center justify-between"},Qe={class:"text-2xl font-bold text-green-600 dark:text-green-400"},Xe={class:"bg-white dark:bg-dark-surface rounded-xl p-6 shadow-elegant dark:shadow-elegant-dark border border-gray-100 dark:border-dark-border hover:shadow-lg dark:hover:shadow-2xl transition-all duration-300"},Ye={class:"flex items-center justify-between"},Ze={class:"text-2xl font-bold text-blue-600 dark:text-blue-400"},et={class:"bg-white dark:bg-dark-surface rounded-xl p-6 shadow-elegant dark:shadow-elegant-dark border border-gray-100 dark:border-dark-border hover:shadow-lg dark:hover:shadow-2xl transition-all duration-300"},tt={class:"flex items-center justify-between"},rt={class:"text-2xl font-bold text-orange-600 dark:text-orange-400"},ot={class:"bg-white dark:bg-dark-surface rounded-xl p-6 shadow-elegant dark:shadow-elegant-dark border border-gray-100 dark:border-dark-border"},st={class:"flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-4 sm:space-y-0"},at={class:"flex items-center space-x-3"},dt={key:0,class:"flex items-center space-x-2 ml-4 pl-4 border-l border-gray-200 dark:border-dark-border"},lt={class:"text-sm text-gray-600 dark:text-dark-text-secondary"},nt={class:"relative"},it={class:"bg-white dark:bg-dark-surface rounded-xl shadow-elegant dark:shadow-elegant-dark border border-gray-100 dark:border-dark-border overflow-hidden"},ut={class:"overflow-x-auto"},ct={class:"flex justify-center"},pt={class:"font-medium text-gray-900 dark:text-dark-text"},xt={class:"text-xs text-gray-500 dark:text-dark-text-secondary mt-1"},bt={class:"font-medium text-green-600 dark:text-green-400"},gt={class:"text-sm font-medium text-blue-600 dark:text-blue-400"},mt={class:"flex items-center space-x-2"},kt={class:"w-6 h-6 bg-gradient-to-br from-purple-400 to-purple-500 rounded-full flex items-center justify-center"},ht={class:"text-white text-xs font-medium"},vt={class:"text-sm text-gray-900 dark:text-dark-text"},ft={class:"text-sm text-gray-600 dark:text-dark-text-secondary"},wt={class:"flex items-center space-x-2"},yt=["onClick"],_t=["onClick"],Pt={class:"flex justify-between items-center px-6 py-4 border-t border-gray-100 dark:border-dark-border bg-gray-50 dark:bg-dark-card/50"},Ct={class:"text-sm text-gray-600 dark:text-dark-text-secondary"},jt=O({__name:"index",setup(T){const f=c(!1),w=c(!1),P=c(null),b=c([]),g=c(""),y=c(86),M=c(42),C=c(8),j=c(89.99),u=c({currentPage:1,pageSize:10,total:0}),a=c([{id:"POD001",name:"个性化T恤 - 猫咪图案",baseProduct:"纯棉圆领T恤",minPrice:79.9,skuCount:6,coverImage:"https://picsum.photos/400/400?random=101",publishStatus:"published",creator:"张三",createTime:"2024-01-15 14:30:25"},{id:"POD002",name:"定制马克杯 - 风景图案",baseProduct:"陶瓷马克杯",minPrice:45.9,skuCount:3,coverImage:"https://picsum.photos/400/400?random=102",publishStatus:"unpublished",creator:"李四",createTime:"2024-01-15 13:45:12"},{id:"POD003",name:"艺术帆布包 - 抽象图案",baseProduct:"帆布手提袋",minPrice:89.9,skuCount:4,coverImage:"https://picsum.photos/400/400?random=103",publishStatus:"publishing",creator:"王五",createTime:"2024-01-15 12:20:08"},{id:"POD004",name:"个性化手机壳 - 几何图案",baseProduct:"iPhone手机壳",minPrice:39.9,skuCount:8,coverImage:"https://picsum.photos/400/400?random=104",publishStatus:"published",creator:"赵六",createTime:"2024-01-15 11:15:33"},{id:"POD005",name:"定制抱枕 - 卡通图案",baseProduct:"方形抱枕",minPrice:69.9,skuCount:5,coverImage:"https://picsum.photos/400/400?random=105",publishStatus:"unpublished",creator:"钱七",createTime:"2024-01-15 10:30:45"}]),t=I(()=>{let i=a.value;g.value&&(i=i.filter($=>$.name.toLowerCase().includes(g.value.toLowerCase())||$.baseProduct.toLowerCase().includes(g.value.toLowerCase())));const r=(u.value.currentPage-1)*u.value.pageSize,p=r+u.value.pageSize;return i.slice(r,p)});Q(()=>{m()});const m=()=>{f.value=!0,setTimeout(()=>{u.value.total=a.value.length,f.value=!1},500)},S=i=>{const r={published:"bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-300",publishing:"bg-yellow-100 dark:bg-yellow-900/30 text-yellow-800 dark:text-yellow-300",unpublished:"bg-gray-100 dark:bg-gray-900/30 text-gray-800 dark:text-gray-300"};return r[i]||r.unpublished},k=i=>({published:"已刊登",publishing:"刊登中",unpublished:"未刊登"})[i]||"未知",z=i=>{b.value=i},B=()=>{u.value.currentPage=1,m()},l=i=>{P.value=i,w.value=!0},N=i=>{_.success(`正在刊登商品：${i.name}`)},E=()=>{_.success("导出POD商品功能开发中...")},F=()=>{_.success(`正在批量刊登 ${b.value.length} 个POD商品...`)},W=i=>{u.value.pageSize=i,u.value.currentPage=1,m()},A=i=>{u.value.currentPage=i,m()};return(i,r)=>{const p=x("el-table-column"),$=x("el-image"),G=x("el-table"),R=x("el-pagination"),q=Z("loading");return v(),V(ee,null,[e("div",Fe,[r[17]||(r[17]=X('<div class="bg-gradient-to-r from-purple-50 to-indigo-50 dark:from-purple-900/20 dark:to-indigo-900/20 rounded-2xl p-6 border border-purple-100 dark:border-purple-800" data-v-5cd1584b><div class="flex items-center space-x-3" data-v-5cd1584b><div class="w-10 h-10 bg-gradient-to-br from-purple-500 to-purple-600 rounded-xl flex items-center justify-center" data-v-5cd1584b><svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24" data-v-5cd1584b><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 4V2a1 1 0 011-1h8a1 1 0 011 1v2m-9 0h10m-10 0a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V6a2 2 0 00-2-2" data-v-5cd1584b></path></svg></div><div data-v-5cd1584b><h1 class="text-2xl font-bold text-gray-900 dark:text-dark-text" data-v-5cd1584b>POD商品</h1><p class="mt-1 text-sm text-gray-600 dark:text-dark-text-secondary" data-v-5cd1584b>按需印刷商品管理和刊登</p></div></div></div>',1)),e("div",We,[e("div",Ae,[e("div",Ge,[e("div",null,[r[4]||(r[4]=e("p",{class:"text-sm font-medium text-gray-600 dark:text-dark-text-secondary"},"POD商品总数",-1)),e("p",Re,s(y.value),1)]),r[5]||(r[5]=e("div",{class:"w-12 h-12 bg-purple-100 dark:bg-purple-900/30 rounded-lg flex items-center justify-center"},[e("svg",{class:"w-6 h-6 text-purple-600 dark:text-purple-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M7 4V2a1 1 0 011-1h8a1 1 0 011 1v2m-9 0h10m-10 0a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V6a2 2 0 00-2-2"})])],-1))])]),e("div",qe,[e("div",Je,[e("div",null,[r[6]||(r[6]=e("p",{class:"text-sm font-medium text-gray-600 dark:text-dark-text-secondary"},"已刊登数量",-1)),e("p",Qe,s(M.value),1)]),r[7]||(r[7]=e("div",{class:"w-12 h-12 bg-green-100 dark:bg-green-900/30 rounded-lg flex items-center justify-center"},[e("svg",{class:"w-6 h-6 text-green-600 dark:text-green-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"})])],-1))])]),e("div",Xe,[e("div",Ye,[e("div",null,[r[8]||(r[8]=e("p",{class:"text-sm font-medium text-gray-600 dark:text-dark-text-secondary"},"今日新增",-1)),e("p",Ze,s(C.value),1)]),r[9]||(r[9]=e("div",{class:"w-12 h-12 bg-blue-100 dark:bg-blue-900/30 rounded-lg flex items-center justify-center"},[e("svg",{class:"w-6 h-6 text-blue-600 dark:text-blue-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"})])],-1))])]),e("div",et,[e("div",tt,[e("div",null,[r[10]||(r[10]=e("p",{class:"text-sm font-medium text-gray-600 dark:text-dark-text-secondary"},"平均价格",-1)),e("p",rt,"¥"+s(j.value),1)]),r[11]||(r[11]=e("div",{class:"w-12 h-12 bg-orange-100 dark:bg-orange-900/30 rounded-lg flex items-center justify-center"},[e("svg",{class:"w-6 h-6 text-orange-600 dark:text-orange-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z"})])],-1))])])]),e("div",ot,[e("div",st,[e("div",at,[r[14]||(r[14]=e("div",{class:"text-sm text-gray-600 dark:text-dark-text-secondary bg-blue-50 dark:bg-blue-900/20 px-3 py-2 rounded-lg border border-blue-200 dark:border-blue-800"},[e("svg",{class:"w-4 h-4 inline mr-2 text-blue-600 dark:text-blue-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"})]),h(' POD商品通过"POD合成"应用自动生成，无法手动创建 ')],-1)),e("button",{onClick:E,class:"inline-flex items-center px-4 py-2.5 bg-white dark:bg-dark-card text-gray-700 dark:text-dark-text font-medium rounded-lg border border-gray-300 dark:border-dark-border hover:bg-gray-50 dark:hover:bg-dark-border shadow-sm hover:shadow-md transition-all duration-200"},r[12]||(r[12]=[e("svg",{class:"w-5 h-5 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4"})],-1),h(" 导出商品 ")])),b.value.length>0?(v(),V("div",dt,[e("span",lt," 已选择 "+s(b.value.length)+" 项 ",1),e("button",{onClick:F,class:"inline-flex items-center px-3 py-1.5 bg-purple-500 hover:bg-purple-600 text-white text-sm font-medium rounded-lg transition-all duration-200"},r[13]||(r[13]=[e("svg",{class:"w-4 h-4 mr-1",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"})],-1),h(" 批量刊登 ")]))])):D("",!0)]),e("div",nt,[U(e("input",{"onUpdate:modelValue":r[0]||(r[0]=o=>g.value=o),type:"text",placeholder:"搜索POD商品...",class:"w-full sm:w-64 pl-10 pr-4 py-2 border border-gray-300 dark:border-dark-border rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500 dark:bg-dark-card dark:text-dark-text",onInput:B},null,544),[[Y,g.value]]),r[15]||(r[15]=e("svg",{class:"w-5 h-5 text-gray-400 absolute left-3 top-2.5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"})],-1))])])]),e("div",it,[r[16]||(r[16]=e("div",{class:"px-6 py-4 border-b border-gray-100 dark:border-dark-border"},[e("h3",{class:"text-lg font-semibold text-gray-900 dark:text-dark-text"},"POD商品列表"),e("p",{class:"text-sm text-gray-600 dark:text-dark-text-secondary mt-1"},"管理您的按需印刷商品")],-1)),e("div",ut,[U((v(),K(G,{data:t.value,style:{width:"100%"},onSelectionChange:z,class:"modern-table","header-cell-style":{backgroundColor:"var(--el-bg-color-page)",color:"var(--el-text-color-primary)",fontWeight:"600",borderBottom:"1px solid var(--el-border-color-light)"},"row-style":{backgroundColor:"transparent"}},{default:n(()=>[d(p,{type:"selection",width:"55"}),d(p,{label:"商品封面图",width:"100"},{default:n(o=>[e("div",ct,[d($,{src:o.row.coverImage,"preview-src-list":[o.row.coverImage],fit:"cover",class:"w-16 h-16 rounded-lg border border-gray-200 dark:border-dark-border","preview-teleported":!0},null,8,["src","preview-src-list"])])]),_:1}),d(p,{prop:"name",label:"商品名称","min-width":"200"},{default:n(o=>[e("div",pt,s(o.row.name),1),e("div",xt," 基于: "+s(o.row.baseProduct),1)]),_:1}),d(p,{prop:"price",label:"价格（最低）",width:"120"},{default:n(o=>[e("span",bt," ¥"+s(o.row.minPrice),1)]),_:1}),d(p,{prop:"skuCount",label:"SKU数量",width:"100"},{default:n(o=>[e("span",gt,s(o.row.skuCount),1)]),_:1}),d(p,{prop:"publishStatus",label:"刊登状态",width:"120"},{default:n(o=>[e("span",{class:L([S(o.row.publishStatus),"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium"])},s(k(o.row.publishStatus)),3)]),_:1}),d(p,{prop:"creator",label:"创建人",width:"100"},{default:n(o=>[e("div",mt,[e("div",kt,[e("span",ht,s(o.row.creator.charAt(0)),1)]),e("span",vt,s(o.row.creator),1)])]),_:1}),d(p,{prop:"createTime",label:"创建时间",width:"180"},{default:n(o=>[e("div",ft,s(o.row.createTime),1)]),_:1}),d(p,{label:"操作",width:"180"},{default:n(o=>[e("div",wt,[e("button",{onClick:J=>l(o.row),class:"inline-flex items-center px-3 py-1.5 text-sm font-medium text-purple-600 dark:text-purple-400 hover:text-purple-700 dark:hover:text-purple-300 bg-purple-50 dark:bg-purple-900/20 hover:bg-purple-100 dark:hover:bg-purple-900/30 rounded-lg transition-all duration-200"}," 查看详情 ",8,yt),e("button",{onClick:J=>N(o.row),class:"inline-flex items-center px-3 py-1.5 text-sm font-medium text-gray-600 dark:text-dark-text-secondary hover:text-gray-700 dark:hover:text-dark-text bg-gray-50 dark:bg-dark-card hover:bg-gray-100 dark:hover:bg-dark-border rounded-lg transition-all duration-200"}," 刊登 ",8,_t)])]),_:1})]),_:1},8,["data"])),[[q,f.value]])]),e("div",Pt,[e("div",Ct," 共 "+s(u.value.total)+" 条记录 ",1),d(R,{"current-page":u.value.currentPage,"onUpdate:currentPage":r[1]||(r[1]=o=>u.value.currentPage=o),"page-size":u.value.pageSize,"onUpdate:pageSize":r[2]||(r[2]=o=>u.value.pageSize=o),"page-sizes":[10,20,50,100],total:u.value.total,layout:"sizes, prev, pager, next, jumper",onSizeChange:W,onCurrentChange:A,class:"modern-pagination"},null,8,["current-page","page-size","total"])])])]),d(Ee,{modelValue:w.value,"onUpdate:modelValue":r[3]||(r[3]=o=>w.value=o),product:P.value},null,8,["modelValue","product"])],64)}}}),$t=H(jt,[["__scopeId","data-v-5cd1584b"]]);export{$t as default};
