import{d as ne,f as M,r as k,h as R,j as i,k as f,a as e,c as x,m as _,p as K,B as y,t as d,l as n,F as U,n as W,E as V,o as u,_ as ie,G as ge,H as xe,b as ue,v as ke,D as ve}from"./index-SDZMOyT2.js";const be={class:"space-y-6"},he={class:"flex items-center justify-center space-x-4 mb-8"},fe={class:"flex items-center"},ye={class:"flex items-center"},we={class:"flex items-center"},_e={key:0,class:"space-y-4"},Ce={class:"flex items-center justify-between"},$e={class:"text-sm text-gray-600 dark:text-dark-text-secondary"},Pe={class:"font-medium text-purple-600 dark:text-purple-400"},je={class:"border border-gray-200 dark:border-dark-border rounded-lg p-4"},Ve={class:"flex items-center space-x-4 mb-4"},Be={class:"grid grid-cols-4 gap-4 max-h-96 overflow-y-auto"},Me=["onClick"],Te={key:0,class:"absolute top-2 right-2 w-6 h-6 bg-purple-500 rounded-full flex items-center justify-center"},De=["src","alt"],ze={class:"text-sm font-medium text-gray-900 dark:text-dark-text truncate"},Se={class:"text-xs text-gray-500 dark:text-dark-text-secondary"},Le={class:"text-sm font-medium text-green-600 dark:text-green-400 mt-1"},Oe={key:1,class:"space-y-4"},Ie={class:"flex space-x-3 mb-4"},Ne={key:0,class:"border border-gray-200 dark:border-dark-border rounded-lg p-4"},He={class:"flex items-center space-x-4 mb-4"},Ue={class:"grid grid-cols-5 gap-4 max-h-96 overflow-y-auto"},Ae=["onClick"],Ge={class:"relative"},Fe=["src","alt"],Ee={key:0,class:"absolute top-1 right-1 w-5 h-5 bg-purple-500 rounded-full flex items-center justify-center"},Re={class:"text-xs font-medium text-gray-900 dark:text-dark-text truncate"},Ke={key:1,class:"border border-gray-200 dark:border-dark-border rounded-lg p-4"},We={key:0,class:"grid grid-cols-5 gap-4 mt-4"},Je=["src","alt"],qe=["onClick"],Qe={key:2,class:"space-y-4"},Xe={class:"bg-gray-50 dark:bg-dark-card rounded-lg p-6"},Ye={class:"grid grid-cols-1 md:grid-cols-2 gap-6"},Ze={class:"text-sm font-medium text-gray-900 dark:text-dark-text mb-3"},et={class:"max-h-64 overflow-y-auto space-y-2"},tt=["src","alt"],rt={class:"flex-1"},st={class:"font-medium text-gray-900 dark:text-dark-text"},ot={class:"text-sm text-gray-500 dark:text-dark-text-secondary"},at={class:"text-sm font-medium text-green-600 dark:text-green-400"},dt={key:0,class:"text-center p-4 text-gray-500 dark:text-dark-text-secondary"},lt={class:"text-sm font-medium text-gray-900 dark:text-dark-text mb-3"},nt={class:"grid grid-cols-3 gap-2 max-h-32 overflow-y-auto"},it=["src","alt"],ut={class:"mt-6 pt-6 border-t border-gray-200 dark:border-dark-border"},ct={class:"grid grid-cols-1 md:grid-cols-2 gap-4"},pt={class:"mt-6 pt-6 border-t border-gray-200 dark:border-dark-border"},mt={class:"grid grid-cols-4 gap-4 text-center"},gt={class:"bg-white dark:bg-dark-surface p-4 rounded-lg border border-gray-200 dark:border-dark-border"},xt={class:"text-2xl font-bold text-orange-600 dark:text-orange-400"},kt={class:"bg-white dark:bg-dark-surface p-4 rounded-lg border border-gray-200 dark:border-dark-border"},vt={class:"text-2xl font-bold text-purple-600 dark:text-purple-400"},bt={class:"bg-white dark:bg-dark-surface p-4 rounded-lg border border-gray-200 dark:border-dark-border"},ht={class:"text-2xl font-bold text-blue-600 dark:text-blue-400"},ft={class:"bg-white dark:bg-dark-surface p-4 rounded-lg border border-gray-200 dark:border-dark-border"},yt={class:"text-2xl font-bold text-green-600 dark:text-green-400"},wt={class:"flex justify-between"},_t={class:"flex space-x-3"},Ct=ne({__name:"CreatePodTaskDialog",props:{modelValue:{type:Boolean}},emits:["update:modelValue","success"],setup(Q,{emit:A}){const B=Q,S=A,z=M({get:()=>B.modelValue,set:p=>S("update:modelValue",p)}),b=k(1),C=k(!1),L=k(""),O=k(""),g=k([]),T=k("gallery"),v=k(""),D=k(""),P=k([]),s=k([]),o=k("个性化"),$=k(20),N=k([{id:"BP001",name:"纯棉圆领T恤",category:"服装",price:39.9,image:"https://picsum.photos/200/200?random=501"},{id:"BP002",name:"陶瓷马克杯",category:"家居",price:25.9,image:"https://picsum.photos/200/200?random=502"},{id:"BP003",name:"帆布手提袋",category:"配饰",price:29.9,image:"https://picsum.photos/200/200?random=503"},{id:"BP004",name:"iPhone手机壳",category:"配饰",price:19.9,image:"https://picsum.photos/200/200?random=504"},{id:"BP005",name:"方形抱枕",category:"家居",price:49.9,image:"https://picsum.photos/200/200?random=505"},{id:"BP006",name:"连帽卫衣",category:"服装",price:89.9,image:"https://picsum.photos/200/200?random=506"},{id:"BP007",name:"棒球帽",category:"配饰",price:35.9,image:"https://picsum.photos/200/200?random=507"},{id:"BP008",name:"保温杯",category:"家居",price:59.9,image:"https://picsum.photos/200/200?random=508"}]),I=k([{id:"PT001",name:"可爱猫咪",category:"animal",image:"https://picsum.photos/150/150?random=601"},{id:"PT002",name:"几何图案",category:"geometric",image:"https://picsum.photos/150/150?random=602"},{id:"PT003",name:"热带植物",category:"plant",image:"https://picsum.photos/150/150?random=603"},{id:"PT004",name:"励志文字",category:"text",image:"https://picsum.photos/150/150?random=604"},{id:"PT005",name:"抽象艺术",category:"geometric",image:"https://picsum.photos/150/150?random=605"},{id:"PT006",name:"卡通狗狗",category:"animal",image:"https://picsum.photos/150/150?random=606"},{id:"PT007",name:"花卉图案",category:"plant",image:"https://picsum.photos/150/150?random=607"},{id:"PT008",name:"英文字母",category:"text",image:"https://picsum.photos/150/150?random=608"}]),J=M(()=>{let p=N.value;return O.value&&(p=p.filter(t=>t.category===O.value)),L.value&&(p=p.filter(t=>t.name.toLowerCase().includes(L.value.toLowerCase()))),p}),q=M(()=>{let p=I.value;return D.value&&(p=p.filter(t=>t.category===D.value)),v.value&&(p=p.filter(t=>t.name.toLowerCase().includes(v.value.toLowerCase()))),p}),c=M(()=>[...P.value,...s.value]),w=M(()=>P.value.length+s.value.length),ee=M(()=>g.value.length===0?0:g.value.length*w.value*6),te=M(()=>g.value.length===0?0:g.value.reduce((t,j)=>t+j.price,0)/g.value.length),re=M(()=>g.value.length===0?0:(te.value+$.value).toFixed(2)),X=M(()=>b.value===1?g.value.length>0:b.value===2?w.value>0:!0),se=p=>{const t=g.value.findIndex(j=>j.id===p.id);t>-1?g.value.splice(t,1):g.value.push(p)},Y=p=>g.value.some(t=>t.id===p),oe=()=>{g.value=[]},ae=p=>{const t=P.value.findIndex(j=>j.id===p.id);t>-1?P.value.splice(t,1):P.value.push(p)},m=p=>{const t=new FileReader;t.onload=j=>{var l;s.value.push({name:p.name,url:(l=j.target)==null?void 0:l.result,file:p.raw})},t.readAsDataURL(p.raw)},r=p=>{s.value.splice(p,1)},h=()=>{},G=()=>{X.value&&b.value++},F=()=>{b.value--},de=()=>{C.value=!0,console.log("选中的白品:",g.value),console.log("选中的图案:",[...P.value,...s.value]),console.log("预计生成商品数:",g.value.length*w.value),setTimeout(()=>{C.value=!1,V.success(`POD合成任务创建成功！将基于 ${g.value.length} 个白品和 ${w.value} 个图案生成 ${g.value.length*w.value} 个商品`),Z(),S("success"),S("update:modelValue",!1)},2e3)},Z=()=>{b.value=1,g.value=[],P.value=[],s.value=[],L.value="",O.value="",v.value="",D.value="",T.value="gallery",o.value="个性化",$.value=20};return(p,t)=>{const j=f("el-input"),l=f("el-option"),le=f("el-select"),E=f("el-button"),ce=f("el-upload"),pe=f("el-input-number"),me=f("el-dialog");return u(),R(me,{modelValue:z.value,"onUpdate:modelValue":t[9]||(t[9]=a=>z.value=a),title:"创建POD合成任务",width:"1000px","align-center":"",onClose:Z,class:"create-pod-dialog"},{footer:i(()=>[e("div",wt,[e("div",null,[b.value>1?(u(),R(E,{key:0,onClick:F},{default:i(()=>t[37]||(t[37]=[y("上一步")])),_:1,__:[37]})):_("",!0)]),e("div",_t,[n(E,{onClick:t[8]||(t[8]=a=>z.value=!1)},{default:i(()=>t[38]||(t[38]=[y("取消")])),_:1,__:[38]}),b.value<3?(u(),R(E,{key:0,type:"primary",onClick:G,disabled:!X.value},{default:i(()=>t[39]||(t[39]=[y(" 下一步 ")])),_:1,__:[39]},8,["disabled"])):(u(),R(E,{key:1,type:"primary",onClick:de,loading:C.value},{default:i(()=>t[40]||(t[40]=[y(" 开始合成 ")])),_:1,__:[40]},8,["loading"]))])])]),default:i(()=>[e("div",be,[e("div",he,[e("div",fe,[e("div",{class:K(["w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium",b.value>=1?"bg-purple-500 text-white":"bg-gray-200 dark:bg-gray-700 text-gray-500"])}," 1 ",2),t[10]||(t[10]=e("span",{class:"ml-2 text-sm font-medium text-gray-900 dark:text-dark-text"},"选择白品",-1))]),t[13]||(t[13]=e("div",{class:"w-16 h-0.5 bg-gray-200 dark:bg-gray-700"},null,-1)),e("div",ye,[e("div",{class:K(["w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium",b.value>=2?"bg-purple-500 text-white":"bg-gray-200 dark:bg-gray-700 text-gray-500"])}," 2 ",2),t[11]||(t[11]=e("span",{class:"ml-2 text-sm font-medium text-gray-900 dark:text-dark-text"},"选择图案",-1))]),t[14]||(t[14]=e("div",{class:"w-16 h-0.5 bg-gray-200 dark:bg-gray-700"},null,-1)),e("div",we,[e("div",{class:K(["w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium",b.value>=3?"bg-purple-500 text-white":"bg-gray-200 dark:bg-gray-700 text-gray-500"])}," 3 ",2),t[12]||(t[12]=e("span",{class:"ml-2 text-sm font-medium text-gray-900 dark:text-dark-text"},"确认合成",-1))])]),b.value===1?(u(),x("div",_e,[e("div",Ce,[t[17]||(t[17]=e("h3",{class:"text-lg font-medium text-gray-900 dark:text-dark-text"},"选择白品商品",-1)),e("div",$e,[t[15]||(t[15]=y(" 已选择 ")),e("span",Pe,d(g.value.length),1),t[16]||(t[16]=y(" 个白品 "))])]),e("div",je,[e("div",Ve,[n(j,{modelValue:L.value,"onUpdate:modelValue":t[0]||(t[0]=a=>L.value=a),placeholder:"搜索白品商品...",style:{width:"300px"},onInput:h},{prefix:i(()=>t[18]||(t[18]=[e("svg",{class:"w-4 h-4 text-gray-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"})],-1)])),_:1},8,["modelValue"]),n(le,{modelValue:O.value,"onUpdate:modelValue":t[1]||(t[1]=a=>O.value=a),placeholder:"选择分类",style:{width:"150px"}},{default:i(()=>[n(l,{label:"全部分类",value:""}),n(l,{label:"服装",value:"clothing"}),n(l,{label:"配饰",value:"accessories"}),n(l,{label:"家居",value:"home"})]),_:1},8,["modelValue"]),g.value.length>0?(u(),R(E,{key:0,onClick:oe,size:"small",type:"danger",plain:""},{default:i(()=>t[19]||(t[19]=[y(" 清空选择 ")])),_:1,__:[19]})):_("",!0)]),e("div",Be,[(u(!0),x(U,null,W(J.value,a=>(u(),x("div",{key:a.id,class:K(["border-2 rounded-lg p-3 cursor-pointer transition-all duration-200 hover:shadow-md relative",Y(a.id)?"border-purple-500 bg-purple-50 dark:bg-purple-900/20":"border-gray-200 dark:border-dark-border hover:border-purple-300"]),onClick:H=>se(a)},[Y(a.id)?(u(),x("div",Te,t[20]||(t[20]=[e("svg",{class:"w-4 h-4 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M5 13l4 4L19 7"})],-1)]))):_("",!0),e("img",{src:a.image,alt:a.name,class:"w-full h-32 object-cover rounded-lg mb-2"},null,8,De),e("h4",ze,d(a.name),1),e("p",Se,d(a.category),1),e("p",Le,"¥"+d(a.price),1)],10,Me))),128))])])])):_("",!0),b.value===2?(u(),x("div",Oe,[t[27]||(t[27]=e("h3",{class:"text-lg font-medium text-gray-900 dark:text-dark-text"},"选择印刷图案",-1)),e("div",Ie,[n(E,{type:T.value==="gallery"?"primary":"default",onClick:t[2]||(t[2]=a=>T.value="gallery")},{default:i(()=>t[21]||(t[21]=[y(" 从图库选择 ")])),_:1,__:[21]},8,["type"]),n(E,{type:T.value==="upload"?"primary":"default",onClick:t[3]||(t[3]=a=>T.value="upload")},{default:i(()=>t[22]||(t[22]=[y(" 上传图片 ")])),_:1,__:[22]},8,["type"])]),T.value==="gallery"?(u(),x("div",Ne,[e("div",He,[n(j,{modelValue:v.value,"onUpdate:modelValue":t[4]||(t[4]=a=>v.value=a),placeholder:"搜索图案...",style:{width:"300px"}},{prefix:i(()=>t[23]||(t[23]=[e("svg",{class:"w-4 h-4 text-gray-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"})],-1)])),_:1},8,["modelValue"]),n(le,{modelValue:D.value,"onUpdate:modelValue":t[5]||(t[5]=a=>D.value=a),placeholder:"选择分类",style:{width:"150px"}},{default:i(()=>[n(l,{label:"全部分类",value:""}),n(l,{label:"动物",value:"animal"}),n(l,{label:"植物",value:"plant"}),n(l,{label:"几何",value:"geometric"}),n(l,{label:"文字",value:"text"})]),_:1},8,["modelValue"])]),e("div",Ue,[(u(!0),x(U,null,W(q.value,a=>(u(),x("div",{key:a.id,class:K(["border-2 rounded-lg p-2 cursor-pointer transition-all duration-200 hover:shadow-md",P.value.some(H=>H.id===a.id)?"border-purple-500 bg-purple-50 dark:bg-purple-900/20":"border-gray-200 dark:border-dark-border hover:border-purple-300"]),onClick:H=>ae(a)},[e("div",Ge,[e("img",{src:a.image,alt:a.name,class:"w-full h-24 object-cover rounded-lg mb-2"},null,8,Fe),P.value.some(H=>H.id===a.id)?(u(),x("div",Ee,t[24]||(t[24]=[e("svg",{class:"w-3 h-3 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M5 13l4 4L19 7"})],-1)]))):_("",!0)]),e("h4",Re,d(a.name),1)],10,Ae))),128))])])):_("",!0),T.value==="upload"?(u(),x("div",Ke,[n(ce,{ref:"uploadRef","file-list":s.value,"on-change":m,"auto-upload":!1,multiple:"",accept:"image/*",drag:"",class:"w-full"},{default:i(()=>t[25]||(t[25]=[e("div",{class:"text-center py-8"},[e("svg",{class:"w-12 h-12 text-gray-400 mx-auto mb-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"})]),e("p",{class:"text-gray-600 dark:text-dark-text-secondary"},"拖拽图片到此处或点击上传"),e("p",{class:"text-sm text-gray-500 dark:text-dark-text-secondary mt-1"},"支持 JPG、PNG 格式")],-1)])),_:1,__:[25]},8,["file-list"]),s.value.length>0?(u(),x("div",We,[(u(!0),x(U,null,W(s.value,(a,H)=>(u(),x("div",{key:H,class:"relative border border-gray-200 dark:border-dark-border rounded-lg p-2"},[e("img",{src:a.url,alt:a.name,class:"w-full h-24 object-cover rounded-lg"},null,8,Je),e("button",{onClick:xs=>r(H),class:"absolute top-1 right-1 w-5 h-5 bg-red-500 text-white rounded-full flex items-center justify-center hover:bg-red-600"},t[26]||(t[26]=[e("svg",{class:"w-3 h-3",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M6 18L18 6M6 6l12 12"})],-1)]),8,qe)]))),128))])):_("",!0)])):_("",!0)])):_("",!0),b.value===3?(u(),x("div",Qe,[t[36]||(t[36]=e("h3",{class:"text-lg font-medium text-gray-900 dark:text-dark-text"},"确认合成信息",-1)),e("div",Xe,[e("div",Ye,[e("div",null,[e("h4",Ze," 选中的白品 ("+d(g.value.length)+") ",1),e("div",et,[(u(!0),x(U,null,W(g.value,a=>(u(),x("div",{key:a.id,class:"flex items-center space-x-3 p-3 border border-gray-200 dark:border-dark-border rounded-lg"},[e("img",{src:a.image,alt:a.name,class:"w-16 h-16 object-cover rounded-lg"},null,8,tt),e("div",rt,[e("p",st,d(a.name),1),e("p",ot,d(a.category),1),e("p",at,"¥"+d(a.price),1)])]))),128)),g.value.length===0?(u(),x("div",dt," 未选择任何白品 ")):_("",!0)])]),e("div",null,[e("h4",lt,"选中的图案 ("+d(w.value)+")",1),e("div",nt,[(u(!0),x(U,null,W(c.value,a=>(u(),x("div",{key:"id"in a?a.id:a.name,class:"relative"},[e("img",{src:"image"in a?a.image:a.url,alt:a.name,class:"w-full h-16 object-cover rounded-lg"},null,8,it)]))),128))])])]),e("div",ut,[t[30]||(t[30]=e("h4",{class:"text-sm font-medium text-gray-900 dark:text-dark-text mb-3"},"合成设置",-1)),e("div",ct,[e("div",null,[t[28]||(t[28]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-dark-text-secondary mb-2"},"商品名称前缀",-1)),n(j,{modelValue:o.value,"onUpdate:modelValue":t[6]||(t[6]=a=>o.value=a),placeholder:"如：个性化、定制"},null,8,["modelValue"])]),e("div",null,[t[29]||(t[29]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-dark-text-secondary mb-2"},"价格调整",-1)),n(pe,{modelValue:$.value,"onUpdate:modelValue":t[7]||(t[7]=a=>$.value=a),min:0,precision:2,step:1,style:{width:"100%"}},null,8,["modelValue"])])])]),e("div",pt,[t[35]||(t[35]=e("h4",{class:"text-sm font-medium text-gray-900 dark:text-dark-text mb-3"},"预计结果",-1)),e("div",mt,[e("div",gt,[e("p",xt,d(g.value.length),1),t[31]||(t[31]=e("p",{class:"text-sm text-gray-600 dark:text-dark-text-secondary"},"白品数量",-1))]),e("div",kt,[e("p",vt,d(g.value.length*w.value),1),t[32]||(t[32]=e("p",{class:"text-sm text-gray-600 dark:text-dark-text-secondary"},"将生成商品数",-1))]),e("div",bt,[e("p",ht,d(ee.value),1),t[33]||(t[33]=e("p",{class:"text-sm text-gray-600 dark:text-dark-text-secondary"},"预计SKU数",-1))]),e("div",ft,[e("p",yt,"¥"+d(re.value),1),t[34]||(t[34]=e("p",{class:"text-sm text-gray-600 dark:text-dark-text-secondary"},"最终售价",-1))])])])])])):_("",!0)])]),_:1},8,["modelValue"])}}}),$t=ie(Ct,[["__scopeId","data-v-882d0e63"]]),Pt={class:"flex items-center justify-between p-6 border-b border-gray-100 dark:border-dark-border"},jt={class:"flex items-center space-x-3"},Vt={class:"text-sm text-gray-600 dark:text-dark-text-secondary"},Bt={key:0,class:"p-6 grid grid-cols-2 md:grid-cols-4 gap-4"},Mt={class:"bg-gradient-to-br from-purple-50 to-purple-100 dark:from-purple-900/20 dark:to-purple-800/20 p-4 rounded-xl border border-purple-200 dark:border-purple-800"},Tt={class:"flex items-center space-x-2"},Dt={class:"text-sm font-bold text-purple-900 dark:text-purple-100"},zt={class:"bg-gradient-to-br from-blue-50 to-blue-100 dark:from-blue-900/20 dark:to-blue-800/20 p-4 rounded-xl border border-blue-200 dark:border-blue-800"},St={class:"flex items-center space-x-2"},Lt={class:"text-sm font-bold text-blue-900 dark:text-blue-100"},Ot={class:"bg-gradient-to-br from-emerald-50 to-emerald-100 dark:from-emerald-900/20 dark:to-emerald-800/20 p-4 rounded-xl border border-emerald-200 dark:border-emerald-800"},It={class:"flex items-center space-x-2"},Nt={class:"text-sm font-bold text-emerald-900 dark:text-emerald-100"},Ht={class:"bg-gradient-to-br from-amber-50 to-amber-100 dark:from-amber-900/20 dark:to-amber-800/20 p-4 rounded-xl border border-amber-200 dark:border-amber-800"},Ut={class:"flex items-center space-x-2"},At={class:"text-sm font-bold text-amber-900 dark:text-amber-100"},Gt={key:1,class:"px-6 pb-6 space-y-6"},Ft={class:"flex items-center justify-between"},Et={class:"flex space-x-3"},Rt={class:"grid grid-cols-1 md:grid-cols-2 gap-6"},Kt={class:"bg-white dark:bg-dark-surface rounded-xl border border-gray-200 dark:border-dark-border overflow-hidden"},Wt={class:"p-4"},Jt={class:"flex items-center space-x-4 mb-4"},qt={class:"text-lg font-medium text-gray-900 dark:text-dark-text"},Qt={class:"text-sm text-gray-600 dark:text-dark-text-secondary"},Xt={class:"grid grid-cols-2 gap-4 text-sm"},Yt={class:"font-mono text-gray-900 dark:text-dark-text"},Zt={class:"text-gray-900 dark:text-dark-text"},er={class:"bg-white dark:bg-dark-surface rounded-xl border border-gray-200 dark:border-dark-border overflow-hidden"},tr={class:"p-4"},rr={class:"space-y-4"},sr={class:"text-xs text-gray-500 dark:text-dark-text-secondary mb-3"},or={class:"space-y-3 max-h-48 overflow-y-auto"},ar=["src","alt"],dr={class:"flex-1"},lr={class:"text-sm font-medium text-gray-900 dark:text-dark-text"},nr={class:"text-xs text-green-600 dark:text-green-400"},ir={class:"pt-4 border-t border-gray-100 dark:border-dark-border"},ur={class:"text-xs text-gray-500 dark:text-dark-text-secondary mb-2"},cr={class:"grid grid-cols-3 gap-2"},pr=["src","alt"],mr={key:0,class:"bg-gray-100 dark:bg-gray-700 rounded-lg flex items-center justify-center h-16"},gr={class:"text-xs text-gray-500 dark:text-gray-400"},xr={class:"bg-white dark:bg-dark-surface rounded-xl border border-gray-200 dark:border-dark-border overflow-hidden"},kr={class:"overflow-x-auto"},vr={class:"flex justify-center"},br={class:"text-sm font-medium text-gray-900 dark:text-dark-text"},hr={class:"text-xs text-gray-500 dark:text-dark-text-secondary"},fr={class:"font-medium text-green-600 dark:text-green-400"},yr={class:"font-medium text-gray-900 dark:text-dark-text"},wr={class:"flex space-x-2"},_r=["onClick"],Cr=["onClick"],$r={class:"flex items-center justify-between p-6 border-t border-gray-100 dark:border-dark-border bg-gray-50 dark:bg-dark-card/50"},Pr={class:"text-sm text-gray-600 dark:text-dark-text-secondary"},jr=ne({__name:"ViewDetailsDialog",props:{modelValue:{type:Boolean},task:{}},emits:["update:modelValue"],setup(Q,{emit:A}){const B=Q,S=A,z=M({get:()=>B.modelValue,set:s=>S("update:modelValue",s)}),b=M(()=>B.task?Array.from({length:B.task.successCount},(s,o)=>({id:`POD${String(o+1).padStart(3,"0")}`,name:`${B.task.productName} - 图案${o+1}`,image:`https://picsum.photos/100/100?random=${800+o+1}`,price:"79.90",skuCount:6})):[]),C=()=>{z.value=!1},L=(s,o)=>o===1?[s]:s.includes("、")||s.includes(",")?s.split(/[、,]/).map($=>$.trim()).filter($=>$):Array.from({length:o},($,N)=>`${s} ${N+1}`),O=s=>{const o={completed:"bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-300",processing:"bg-yellow-100 dark:bg-yellow-900/30 text-yellow-800 dark:text-yellow-300",failed:"bg-red-100 dark:bg-red-900/30 text-red-800 dark:text-red-300",pending:"bg-gray-100 dark:bg-gray-900/30 text-gray-800 dark:text-gray-300"};return o[s]||o.pending},g=s=>({completed:"已完成",processing:"处理中",failed:"失败",pending:"等待中"})[s]||"未知",T=s=>{V.info(`查看POD商品: ${s.name}`)},v=s=>{V.success(`正在刊登POD商品: ${s.name}`)},D=()=>{var s;V.success(`正在批量刊登 ${((s=B.task)==null?void 0:s.successCount)||0} 个POD商品`)},P=()=>{var s;V.success(`正在下载 ${((s=B.task)==null?void 0:s.successCount)||0} 个商品图片`)};return(s,o)=>{const $=f("el-button"),N=f("el-image"),I=f("el-table-column"),J=f("el-table"),q=f("el-dialog");return u(),R(q,{modelValue:z.value,"onUpdate:modelValue":o[0]||(o[0]=c=>z.value=c),width:"1200px","before-close":C,"show-close":!1,class:"modern-dialog"},{header:i(()=>{var c;return[e("div",Pt,[e("div",jt,[o[2]||(o[2]=e("div",{class:"w-10 h-10 bg-gradient-to-br from-purple-500 to-pink-600 rounded-xl flex items-center justify-center"},[e("svg",{class:"w-6 h-6 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M15.232 5.232l3.536 3.536m-2.036-5.036a2.5 2.5 0 113.536 3.536L6.5 21.036H3v-3.572L16.732 3.732z"})])],-1)),e("div",null,[o[1]||(o[1]=e("h3",{class:"text-xl font-bold text-gray-900 dark:text-dark-text"},"POD合成任务详情",-1)),e("p",Vt,"任务ID: "+d(((c=s.task)==null?void 0:c.id)||""),1)])]),e("button",{onClick:C,class:"p-2 text-gray-400 hover:text-gray-600 dark:text-dark-text-secondary dark:hover:text-dark-text rounded-lg hover:bg-gray-100 dark:hover:bg-dark-card transition-all duration-200"},o[3]||(o[3]=[e("svg",{class:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M6 18L18 6M6 6l12 12"})],-1)]))])]}),footer:i(()=>{var c;return[e("div",$r,[e("div",Pr," 共生成 "+d(((c=s.task)==null?void 0:c.successCount)||0)+" 个POD商品 ",1),e("div",{class:"flex items-center space-x-3"},[e("button",{onClick:C,class:"px-6 py-2.5 text-gray-700 dark:text-dark-text font-medium rounded-lg border border-gray-300 dark:border-dark-border hover:bg-gray-50 dark:hover:bg-dark-border transition-all duration-200"}," 关闭 "),e("button",{onClick:D,class:"inline-flex items-center px-6 py-2.5 bg-gradient-to-r from-purple-500 to-purple-600 hover:from-purple-600 hover:to-purple-700 text-white font-medium rounded-lg shadow-lg hover:shadow-xl transition-all duration-200 transform hover:scale-105"},o[22]||(o[22]=[e("svg",{class:"w-5 h-5 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"})],-1),y(" 批量刊登 ")]))])])]}),default:i(()=>[s.task?(u(),x("div",Bt,[e("div",Mt,[e("div",Tt,[o[5]||(o[5]=e("div",{class:"w-8 h-8 bg-purple-500 rounded-lg flex items-center justify-center"},[e("svg",{class:"w-4 h-4 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"})])],-1)),e("div",null,[o[4]||(o[4]=e("p",{class:"text-xs text-purple-600 dark:text-purple-400 font-medium"},"任务状态",-1)),e("p",Dt,d(g(s.task.status)),1)])])]),e("div",zt,[e("div",St,[o[7]||(o[7]=e("div",{class:"w-8 h-8 bg-blue-500 rounded-lg flex items-center justify-center"},[e("svg",{class:"w-4 h-4 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"})])],-1)),e("div",null,[o[6]||(o[6]=e("p",{class:"text-xs text-blue-600 dark:text-blue-400 font-medium"},"合成数量",-1)),e("p",Lt,d(s.task.baseCount)+" × "+d(s.task.patternCount),1)])])]),e("div",Ot,[e("div",It,[o[9]||(o[9]=e("div",{class:"w-8 h-8 bg-emerald-500 rounded-lg flex items-center justify-center"},[e("svg",{class:"w-4 h-4 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"})])],-1)),e("div",null,[o[8]||(o[8]=e("p",{class:"text-xs text-emerald-600 dark:text-emerald-400 font-medium"},"成功数量",-1)),e("p",Nt,d(s.task.successCount),1)])])]),e("div",Ht,[e("div",Ut,[o[11]||(o[11]=e("div",{class:"w-8 h-8 bg-amber-500 rounded-lg flex items-center justify-center"},[e("svg",{class:"w-4 h-4 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"})])],-1)),e("div",null,[o[10]||(o[10]=e("p",{class:"text-xs text-amber-600 dark:text-amber-400 font-medium"},"操作人",-1)),e("p",At,d(s.task.operator),1)])])])])):_("",!0),s.task?(u(),x("div",Gt,[e("div",Ft,[o[14]||(o[14]=e("h3",{class:"text-lg font-semibold text-gray-900 dark:text-dark-text"},"合成详情",-1)),e("div",Et,[n($,{type:"primary",size:"small",onClick:D},{default:i(()=>o[12]||(o[12]=[e("svg",{class:"w-4 h-4 mr-1",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"})],-1),y(" 批量刊登 ")])),_:1,__:[12]}),n($,{size:"small",onClick:P},{default:i(()=>o[13]||(o[13]=[e("svg",{class:"w-4 h-4 mr-1",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4"})],-1),y(" 下载图片 ")])),_:1,__:[13]})])]),e("div",Rt,[e("div",Kt,[o[17]||(o[17]=e("div",{class:"p-4 border-b border-gray-100 dark:border-dark-border"},[e("h4",{class:"text-sm font-medium text-gray-900 dark:text-dark-text"},"合成结果")],-1)),e("div",Wt,[e("div",Jt,[n(N,{src:s.task.resultImage,"preview-src-list":[s.task.resultImage],fit:"cover",class:"w-24 h-24 rounded-lg border border-gray-200 dark:border-dark-border","preview-teleported":!0},null,8,["src","preview-src-list"]),e("div",null,[e("h4",qt,d(s.task.productName),1),e("p",Qt,"基于: "+d(s.task.baseProduct),1),e("span",{class:K([O(s.task.status),"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium mt-2"])},d(g(s.task.status)),3)])]),e("div",Xt,[e("div",null,[o[15]||(o[15]=e("p",{class:"text-gray-500 dark:text-dark-text-secondary"},"任务ID",-1)),e("p",Yt,d(s.task.id),1)]),e("div",null,[o[16]||(o[16]=e("p",{class:"text-gray-500 dark:text-dark-text-secondary"},"创建时间",-1)),e("p",Zt,d(s.task.createTime),1)])])])]),e("div",er,[o[18]||(o[18]=e("div",{class:"p-4 border-b border-gray-100 dark:border-dark-border"},[e("h4",{class:"text-sm font-medium text-gray-900 dark:text-dark-text"},"合成材料")],-1)),e("div",tr,[e("div",rr,[e("div",null,[e("p",sr,"基础商品 ("+d(s.task.baseCount)+")",1),e("div",or,[(u(!0),x(U,null,W(L(s.task.baseProduct,s.task.baseCount),(c,w)=>(u(),x("div",{key:w,class:"flex items-center space-x-3 p-3 bg-gray-50 dark:bg-gray-800/50 rounded-lg"},[e("img",{src:`https://picsum.photos/100/100?random=${701+w}`,alt:c,class:"w-12 h-12 rounded-lg object-cover"},null,8,ar),e("div",dr,[e("p",lr,d(c),1),e("p",nr,"白品 "+d(w+1),1)])]))),128))])]),e("div",ir,[e("p",ur,"印刷图案 ("+d(s.task.patternCount)+")",1),e("div",cr,[(u(!0),x(U,null,W(Math.min(s.task.patternCount,6),c=>(u(),x("div",{key:c,class:"relative"},[e("img",{src:`https://picsum.photos/100/100?random=${700+c}`,alt:`图案${c}`,class:"w-full h-16 rounded-lg object-cover"},null,8,pr)]))),128)),s.task.patternCount>6?(u(),x("div",mr,[e("span",gr,"+"+d(s.task.patternCount-6),1)])):_("",!0)])])])])])]),e("div",xr,[o[21]||(o[21]=e("div",{class:"p-4 border-b border-gray-100 dark:border-dark-border"},[e("h4",{class:"text-sm font-medium text-gray-900 dark:text-dark-text"},"生成的POD商品")],-1)),e("div",kr,[n(J,{data:b.value,style:{width:"100%"},"max-height":"400",class:"modern-table","header-cell-style":{backgroundColor:"var(--el-bg-color-page)",color:"var(--el-text-color-primary)",fontWeight:"600",borderBottom:"1px solid var(--el-border-color-light)"},"row-style":{backgroundColor:"transparent"}},{default:i(()=>[n(I,{label:"商品图片",width:"100"},{default:i(c=>[e("div",vr,[n(N,{src:c.row.image,"preview-src-list":[c.row.image],fit:"cover",class:"w-12 h-12 rounded-lg border border-gray-200 dark:border-dark-border","preview-teleported":!0},null,8,["src","preview-src-list"])])]),_:1}),n(I,{prop:"name",label:"商品名称","min-width":"200"},{default:i(c=>[e("div",null,[e("p",br,d(c.row.name),1),e("p",hr,d(c.row.id),1)])]),_:1}),n(I,{prop:"price",label:"价格",width:"100"},{default:i(c=>[e("span",fr,"¥"+d(c.row.price),1)]),_:1}),n(I,{prop:"skuCount",label:"SKU数量",width:"100"},{default:i(c=>[e("span",yr,d(c.row.skuCount),1)]),_:1}),n(I,{label:"操作",width:"150"},{default:i(c=>[e("div",wr,[e("button",{onClick:w=>T(c.row),class:"text-purple-600 hover:text-purple-700 transition-colors"},o[19]||(o[19]=[e("svg",{class:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M15 12a3 3 0 11-6 0 3 3 0 016 0z"}),e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"})],-1)]),8,_r),e("button",{onClick:w=>v(c.row),class:"text-blue-600 hover:text-blue-700 transition-colors"},o[20]||(o[20]=[e("svg",{class:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"})],-1)]),8,Cr)])]),_:1})]),_:1},8,["data"])])])])):_("",!0)]),_:1},8,["modelValue"])}}}),Vr=ie(jr,[["__scopeId","data-v-d37f4746"]]),Br={class:"space-y-6"},Mr={class:"grid grid-cols-1 md:grid-cols-4 gap-6"},Tr={class:"bg-white dark:bg-dark-surface rounded-xl p-6 shadow-elegant dark:shadow-elegant-dark border border-gray-100 dark:border-dark-border hover:shadow-lg dark:hover:shadow-2xl transition-all duration-300"},Dr={class:"flex items-center justify-between"},zr={class:"text-2xl font-bold text-gray-900 dark:text-dark-text"},Sr={class:"bg-white dark:bg-dark-surface rounded-xl p-6 shadow-elegant dark:shadow-elegant-dark border border-gray-100 dark:border-dark-border hover:shadow-lg dark:hover:shadow-2xl transition-all duration-300"},Lr={class:"flex items-center justify-between"},Or={class:"text-2xl font-bold text-green-600 dark:text-green-400"},Ir={class:"bg-white dark:bg-dark-surface rounded-xl p-6 shadow-elegant dark:shadow-elegant-dark border border-gray-100 dark:border-dark-border hover:shadow-lg dark:hover:shadow-2xl transition-all duration-300"},Nr={class:"flex items-center justify-between"},Hr={class:"text-2xl font-bold text-blue-600 dark:text-blue-400"},Ur={class:"bg-white dark:bg-dark-surface rounded-xl p-6 shadow-elegant dark:shadow-elegant-dark border border-gray-100 dark:border-dark-border hover:shadow-lg dark:hover:shadow-2xl transition-all duration-300"},Ar={class:"flex items-center justify-between"},Gr={class:"text-2xl font-bold text-orange-600 dark:text-orange-400"},Fr={class:"bg-white dark:bg-dark-surface rounded-xl p-6 shadow-elegant dark:shadow-elegant-dark border border-gray-100 dark:border-dark-border"},Er={class:"flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-4 sm:space-y-0"},Rr={class:"flex items-center space-x-3"},Kr={key:0,class:"flex items-center space-x-2 ml-4 pl-4 border-l border-gray-200 dark:border-dark-border"},Wr={class:"text-sm text-gray-600 dark:text-dark-text-secondary"},Jr={class:"relative"},qr={class:"bg-white dark:bg-dark-surface rounded-xl shadow-elegant dark:shadow-elegant-dark border border-gray-100 dark:border-dark-border overflow-hidden"},Qr={class:"overflow-x-auto"},Xr={class:"font-mono text-sm text-gray-600 dark:text-dark-text-secondary"},Yr={class:"flex items-center space-x-3"},Zr={class:"font-medium text-gray-900 dark:text-dark-text"},es={class:"text-xs text-gray-500 dark:text-dark-text-secondary mt-1"},ts={class:"flex items-center space-x-2"},rs={class:"font-medium text-gray-900 dark:text-dark-text"},ss={class:"font-medium text-purple-600 dark:text-purple-400"},os={class:"text-sm font-medium text-green-600 dark:text-green-400"},as={class:"flex items-center space-x-2"},ds={class:"w-6 h-6 bg-gradient-to-br from-purple-400 to-pink-500 rounded-full flex items-center justify-center"},ls={class:"text-white text-xs font-medium"},ns={class:"text-sm text-gray-900 dark:text-dark-text"},is={class:"text-sm text-gray-600 dark:text-dark-text-secondary"},us={class:"flex items-center space-x-2"},cs=["onClick"],ps={class:"flex justify-between items-center px-6 py-4 border-t border-gray-100 dark:border-dark-border bg-gray-50 dark:bg-dark-card/50"},ms={class:"text-sm text-gray-600 dark:text-dark-text-secondary"},gs=ne({__name:"index",setup(Q){const A=k(!1),B=k(!1),S=k(!1),z=k(null),b=k([]),C=k(""),L=k(156),O=k(128),g=k(18),T=k(12),v=k({currentPage:1,pageSize:10,total:0}),D=k([{id:"POD001",productName:"个性化T恤 - 猫咪图案",baseProduct:"纯棉圆领T恤",baseCount:1,patternCount:1,status:"completed",successCount:6,resultImage:"https://picsum.photos/400/400?random=401",operator:"张三",createTime:"2024-01-15 14:30:25"},{id:"POD002",productName:"多品类合成 - 风景图案",baseProduct:"陶瓷马克杯、保温杯、方形抱枕",baseCount:3,patternCount:2,status:"processing",successCount:0,resultImage:"https://picsum.photos/400/400?random=402",operator:"李四",createTime:"2024-01-15 13:45:12"},{id:"POD003",productName:"艺术帆布包 - 抽象图案",baseProduct:"帆布手提袋",baseCount:1,patternCount:2,status:"completed",successCount:8,resultImage:"https://picsum.photos/400/400?random=403",operator:"王五",createTime:"2024-01-15 12:20:08"},{id:"POD004",productName:"服装系列合成 - 几何图案",baseProduct:"纯棉圆领T恤、连帽卫衣",baseCount:2,patternCount:1,status:"failed",successCount:0,resultImage:"https://picsum.photos/400/400?random=404",operator:"赵六",createTime:"2024-01-15 11:15:33"},{id:"POD005",productName:"定制抱枕 - 卡通图案",baseProduct:"方形抱枕",baseCount:1,patternCount:3,status:"completed",successCount:12,resultImage:"https://picsum.photos/400/400?random=405",operator:"钱七",createTime:"2024-01-15 10:30:45"},{id:"POD006",productName:"全品类合成 - 动物主题",baseProduct:"纯棉圆领T恤、陶瓷马克杯、帆布手提袋、iPhone手机壳、棒球帽",baseCount:5,patternCount:4,status:"completed",successCount:60,resultImage:"https://picsum.photos/400/400?random=406",operator:"孙八",createTime:"2024-01-15 09:15:20"},{id:"POD007",productName:"家居用品合成 - 简约风格",baseProduct:"陶瓷马克杯、保温杯、方形抱枕",baseCount:3,patternCount:2,status:"completed",successCount:18,resultImage:"https://picsum.photos/400/400?random=407",operator:"周九",createTime:"2024-01-15 08:30:15"}]),P=M(()=>{let m=D.value;C.value&&(m=m.filter(G=>G.id.toLowerCase().includes(C.value.toLowerCase())||G.productName.toLowerCase().includes(C.value.toLowerCase())||G.baseProduct.toLowerCase().includes(C.value.toLowerCase())));const r=(v.value.currentPage-1)*v.value.pageSize,h=r+v.value.pageSize;return m.slice(r,h)});ge(()=>{s()});const s=()=>{A.value=!0,setTimeout(()=>{v.value.total=D.value.length,A.value=!1},500)},o=m=>{const r={completed:"bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-300",processing:"bg-yellow-100 dark:bg-yellow-900/30 text-yellow-800 dark:text-yellow-300",failed:"bg-red-100 dark:bg-red-900/30 text-red-800 dark:text-red-300",pending:"bg-gray-100 dark:bg-gray-900/30 text-gray-800 dark:text-gray-300"};return r[m]||r.pending},$=m=>({completed:"已完成",processing:"处理中",failed:"失败",pending:"等待中"})[m]||"未知",N=m=>{b.value=m},I=()=>{v.value.currentPage=1,s()},J=m=>{z.value=m,S.value=!0},q=m=>{const{action:r,row:h}=m;switch(r){case"smartCrop":c(h);break;case"oneClickCutout":w(h);break;case"superSplit":ee(h);break;case"titleGenerate":te(h);break;case"batchListing":re(h);break}},c=m=>{V.success(`正在为POD合成任务 ${m.id} 创建智能裁图任务...`)},w=m=>{V.success(`正在为POD合成任务 ${m.id} 创建一键抠图任务...`)},ee=m=>{V.success(`正在为POD合成任务 ${m.id} 创建超级裂变任务...`)},te=m=>{V.success(`正在为POD合成任务 ${m.id} 创建标题生成任务...`)},re=m=>{V.success(`正在为POD合成任务 ${m.id} 创建批量刊登任务...`)},X=()=>{V.success("导出任务功能开发中...")},se=()=>{V.success(`正在批量导出 ${b.value.length} 个任务...`)},Y=()=>{V.success("操作成功！"),s()},oe=m=>{v.value.pageSize=m,v.value.currentPage=1,s()},ae=m=>{v.value.currentPage=m,s()};return(m,r)=>{const h=f("el-table-column"),G=f("el-image"),F=f("el-dropdown-item"),de=f("el-dropdown-menu"),Z=f("el-dropdown"),p=f("el-table"),t=f("el-pagination"),j=ve("loading");return u(),x(U,null,[e("div",Br,[r[28]||(r[28]=xe('<div class="bg-gradient-to-r from-purple-50 to-pink-50 dark:from-purple-900/20 dark:to-pink-900/20 rounded-2xl p-6 border border-purple-100 dark:border-purple-800" data-v-2e09f4d2><div class="flex items-center space-x-3" data-v-2e09f4d2><div class="w-10 h-10 bg-gradient-to-br from-purple-500 to-pink-600 rounded-xl flex items-center justify-center" data-v-2e09f4d2><svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24" data-v-2e09f4d2><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15.232 5.232l3.536 3.536m-2.036-5.036a2.5 2.5 0 113.536 3.536L6.5 21.036H3v-3.572L16.732 3.732z" data-v-2e09f4d2></path></svg></div><div data-v-2e09f4d2><h1 class="text-2xl font-bold text-gray-900 dark:text-dark-text" data-v-2e09f4d2>POD合成</h1><p class="mt-1 text-sm text-gray-600 dark:text-dark-text-secondary" data-v-2e09f4d2>选择白品和图案，创建按需印刷商品</p></div></div></div>',1)),e("div",Mr,[e("div",Tr,[e("div",Dr,[e("div",null,[r[6]||(r[6]=e("p",{class:"text-sm font-medium text-gray-600 dark:text-dark-text-secondary"},"合成任务总数",-1)),e("p",zr,d(L.value),1)]),r[7]||(r[7]=e("div",{class:"w-12 h-12 bg-purple-100 dark:bg-purple-900/30 rounded-lg flex items-center justify-center"},[e("svg",{class:"w-6 h-6 text-purple-600 dark:text-purple-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"})])],-1))])]),e("div",Sr,[e("div",Lr,[e("div",null,[r[8]||(r[8]=e("p",{class:"text-sm font-medium text-gray-600 dark:text-dark-text-secondary"},"成功数量",-1)),e("p",Or,d(O.value),1)]),r[9]||(r[9]=e("div",{class:"w-12 h-12 bg-green-100 dark:bg-green-900/30 rounded-lg flex items-center justify-center"},[e("svg",{class:"w-6 h-6 text-green-600 dark:text-green-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"})])],-1))])]),e("div",Ir,[e("div",Nr,[e("div",null,[r[10]||(r[10]=e("p",{class:"text-sm font-medium text-gray-600 dark:text-dark-text-secondary"},"今日新增",-1)),e("p",Hr,d(g.value),1)]),r[11]||(r[11]=e("div",{class:"w-12 h-12 bg-blue-100 dark:bg-blue-900/30 rounded-lg flex items-center justify-center"},[e("svg",{class:"w-6 h-6 text-blue-600 dark:text-blue-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"})])],-1))])]),e("div",Ur,[e("div",Ar,[e("div",null,[r[12]||(r[12]=e("p",{class:"text-sm font-medium text-gray-600 dark:text-dark-text-secondary"},"处理中",-1)),e("p",Gr,d(T.value),1)]),r[13]||(r[13]=e("div",{class:"w-12 h-12 bg-orange-100 dark:bg-orange-900/30 rounded-lg flex items-center justify-center"},[e("svg",{class:"w-6 h-6 text-orange-600 dark:text-orange-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"})])],-1))])])]),e("div",Fr,[e("div",Er,[e("div",Rr,[e("button",{onClick:r[0]||(r[0]=l=>B.value=!0),class:"inline-flex items-center px-4 py-2.5 bg-gradient-to-r from-purple-500 to-pink-600 hover:from-purple-600 hover:to-pink-700 text-white font-medium rounded-lg shadow-lg hover:shadow-xl transition-all duration-200 transform hover:scale-105"},r[14]||(r[14]=[e("svg",{class:"w-5 h-5 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 6v6m0 0v6m0-6h6m-6 0H6"})],-1),y(" 创建合成任务 ")])),e("button",{onClick:X,class:"inline-flex items-center px-4 py-2.5 bg-white dark:bg-dark-card text-gray-700 dark:text-dark-text font-medium rounded-lg border border-gray-300 dark:border-dark-border hover:bg-gray-50 dark:hover:bg-dark-border shadow-sm hover:shadow-md transition-all duration-200"},r[15]||(r[15]=[e("svg",{class:"w-5 h-5 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4"})],-1),y(" 导出任务 ")])),b.value.length>0?(u(),x("div",Kr,[e("span",Wr," 已选择 "+d(b.value.length)+" 项 ",1),e("button",{onClick:se,class:"inline-flex items-center px-3 py-1.5 bg-purple-500 hover:bg-purple-600 text-white text-sm font-medium rounded-lg transition-all duration-200"},r[16]||(r[16]=[e("svg",{class:"w-4 h-4 mr-1",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4"})],-1),y(" 批量导出 ")]))])):_("",!0)]),e("div",Jr,[ue(e("input",{"onUpdate:modelValue":r[1]||(r[1]=l=>C.value=l),type:"text",placeholder:"搜索任务ID、商品名称...",class:"w-full sm:w-64 pl-10 pr-4 py-2 border border-gray-300 dark:border-dark-border rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500 dark:bg-dark-card dark:text-dark-text",onInput:I},null,544),[[ke,C.value]]),r[17]||(r[17]=e("svg",{class:"w-5 h-5 text-gray-400 absolute left-3 top-2.5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"})],-1))])])]),e("div",qr,[r[27]||(r[27]=e("div",{class:"px-6 py-4 border-b border-gray-100 dark:border-dark-border"},[e("h3",{class:"text-lg font-semibold text-gray-900 dark:text-dark-text"},"POD合成任务"),e("p",{class:"text-sm text-gray-600 dark:text-dark-text-secondary mt-1"},"管理您的POD合成任务")],-1)),e("div",Qr,[ue((u(),R(p,{data:P.value,style:{width:"100%"},onSelectionChange:N,class:"modern-table","header-cell-style":{backgroundColor:"var(--el-bg-color-page)",color:"var(--el-text-color-primary)",fontWeight:"600",borderBottom:"1px solid var(--el-border-color-light)"},"row-style":{backgroundColor:"transparent"}},{default:i(()=>[n(h,{type:"selection",width:"55"}),n(h,{prop:"id",label:"任务ID",width:"120"},{default:i(l=>[e("span",Xr,d(l.row.id),1)]),_:1}),n(h,{label:"商品信息","min-width":"200"},{default:i(l=>[e("div",Yr,[n(G,{src:l.row.resultImage,"preview-src-list":[l.row.resultImage],fit:"cover",class:"w-12 h-12 rounded-lg border border-gray-200 dark:border-dark-border","preview-teleported":!0},null,8,["src","preview-src-list"]),e("div",null,[e("div",Zr,d(l.row.productName),1),e("div",es," 基于: "+d(l.row.baseProduct),1)])])]),_:1}),n(h,{label:"合成信息",width:"150"},{default:i(l=>[e("div",ts,[r[18]||(r[18]=e("span",{class:"text-sm text-gray-600 dark:text-dark-text-secondary"},"白品:",-1)),e("span",rs,d(l.row.baseCount),1),r[19]||(r[19]=e("span",{class:"text-gray-400"},"|",-1)),r[20]||(r[20]=e("span",{class:"text-sm text-gray-600 dark:text-dark-text-secondary"},"图案:",-1)),e("span",ss,d(l.row.patternCount),1)])]),_:1}),n(h,{prop:"status",label:"合成状态",width:"120"},{default:i(l=>[e("span",{class:K([o(l.row.status),"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium"])},d($(l.row.status)),3)]),_:1}),n(h,{prop:"successCount",label:"成功数量",width:"100"},{default:i(l=>[e("span",os,d(l.row.successCount),1)]),_:1}),n(h,{prop:"operator",label:"操作人",width:"100"},{default:i(l=>[e("div",as,[e("div",ds,[e("span",ls,d(l.row.operator.charAt(0)),1)]),e("span",ns,d(l.row.operator),1)])]),_:1}),n(h,{prop:"createTime",label:"创建时间",width:"180"},{default:i(l=>[e("div",is,d(l.row.createTime),1)]),_:1}),n(h,{label:"操作",width:"180"},{default:i(l=>[e("div",us,[e("button",{onClick:le=>J(l.row),class:"inline-flex items-center px-3 py-1.5 text-sm font-medium text-purple-600 dark:text-purple-400 hover:text-purple-700 dark:hover:text-purple-300 bg-purple-50 dark:bg-purple-900/20 hover:bg-purple-100 dark:hover:bg-purple-900/30 rounded-lg transition-all duration-200"}," 查看详情 ",8,cs),n(Z,{onCommand:q,trigger:"click"},{dropdown:i(()=>[n(de,null,{default:i(()=>[n(F,{command:{action:"smartCrop",row:l.row}},{default:i(()=>r[21]||(r[21]=[e("div",{class:"flex items-center space-x-2"},[e("svg",{class:"w-4 h-4 text-purple-500",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M7 4V2a1 1 0 011-1h8a1 1 0 011 1v2m0 0V1a1 1 0 011-1h2a1 1 0 011 1v18a1 1 0 01-1 1H4a1 1 0 01-1-1V1a1 1 0 011-1h2a1 1 0 011 1v3m0 0h8m-8 0H4a1 1 0 00-1 1v3a1 1 0 001 1h3m0 0h10m0 0h3a1 1 0 001-1V5a1 1 0 00-1-1h-3m-3 0v8a1 1 0 01-1 1H8a1 1 0 01-1-1V4"})]),e("span",null,"智能裁图")],-1)])),_:2,__:[21]},1032,["command"]),n(F,{command:{action:"oneClickCutout",row:l.row}},{default:i(()=>r[22]||(r[22]=[e("div",{class:"flex items-center space-x-2"},[e("svg",{class:"w-4 h-4 text-pink-500",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"})]),e("span",null,"一键抠图")],-1)])),_:2,__:[22]},1032,["command"]),n(F,{command:{action:"superSplit",row:l.row}},{default:i(()=>r[23]||(r[23]=[e("div",{class:"flex items-center space-x-2"},[e("svg",{class:"w-4 h-4 text-orange-500",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M13 10V3L4 14h7v7l9-11h-7z"})]),e("span",null,"超级裂变")],-1)])),_:2,__:[23]},1032,["command"]),n(F,{command:{action:"titleGenerate",row:l.row}},{default:i(()=>r[24]||(r[24]=[e("div",{class:"flex items-center space-x-2"},[e("svg",{class:"w-4 h-4 text-blue-500",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"})]),e("span",null,"标题生成")],-1)])),_:2,__:[24]},1032,["command"]),n(F,{command:{action:"batchListing",row:l.row}},{default:i(()=>r[25]||(r[25]=[e("div",{class:"flex items-center space-x-2"},[e("svg",{class:"w-4 h-4 text-green-500",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"})]),e("span",null,"批量刊登")],-1)])),_:2,__:[25]},1032,["command"])]),_:2},1024)]),default:i(()=>[r[26]||(r[26]=e("button",{class:"inline-flex items-center px-2 py-1.5 text-sm font-medium text-gray-600 dark:text-dark-text-secondary hover:text-gray-700 dark:hover:text-dark-text bg-gray-50 dark:bg-dark-card hover:bg-gray-100 dark:hover:bg-dark-border rounded-lg transition-all duration-200"},[y(" 更多 "),e("svg",{class:"w-4 h-4 ml-1",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M19 9l-7 7-7-7"})])],-1))]),_:2,__:[26]},1024)])]),_:1})]),_:1},8,["data"])),[[j,A.value]])]),e("div",ps,[e("div",ms," 共 "+d(v.value.total)+" 条记录 ",1),n(t,{"current-page":v.value.currentPage,"onUpdate:currentPage":r[2]||(r[2]=l=>v.value.currentPage=l),"page-size":v.value.pageSize,"onUpdate:pageSize":r[3]||(r[3]=l=>v.value.pageSize=l),"page-sizes":[10,20,50,100],total:v.value.total,layout:"sizes, prev, pager, next, jumper",onSizeChange:oe,onCurrentChange:ae,class:"modern-pagination"},null,8,["current-page","page-size","total"])])])]),n($t,{modelValue:B.value,"onUpdate:modelValue":r[4]||(r[4]=l=>B.value=l),onSuccess:Y},null,8,["modelValue"]),n(Vr,{modelValue:S.value,"onUpdate:modelValue":r[5]||(r[5]=l=>S.value=l),task:z.value},null,8,["modelValue","task"])],64)}}}),vs=ie(gs,[["__scopeId","data-v-2e09f4d2"]]);export{vs as default};
