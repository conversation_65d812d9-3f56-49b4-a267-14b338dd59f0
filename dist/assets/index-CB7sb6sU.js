import{c as n,a as e,o as t,d as q,f as C,p as F,h as I,m as S,k as w,j as y,B as $,t as l,_ as K,w as de,l as p,A as N,F as M,n as D,r as k,g as Ve,H as fe,b as Ie,q as Se,E as X,T as Te,J as Ne,G as Me}from"./index-g0Lcbgij.js";import{r as De}from"./MagnifyingGlassIcon-B6kQhNbF.js";function ze(s,c){return t(),n("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M19.5 13.5 12 21m0 0-7.5-7.5M12 21V3"})])}function Oe(s,c){return t(),n("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M4.5 10.5 12 3m0 0 7.5 7.5M12 3v18"})])}function ve(s,c){return t(),n("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M9 12.75 11.25 15 15 9.75M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z"})])}function je(s,c){return t(),n("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"m4.5 12.75 6 6 9-13.5"})])}function Ue(s,c){return t(),n("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M12 6v12m-3-2.818.879.659c1.171.879 3.07.879 4.242 0 1.172-.879 1.172-2.303 0-3.182C13.536 12.219 12.768 12 12 12c-.725 0-1.45-.22-2.003-.659-1.106-.879-1.106-2.303 0-3.182s2.9-.879 4.006 0l.415.33M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z"})])}function Ee(s,c){return t(),n("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M2.036 12.322a1.012 1.012 0 0 1 0-.639C3.423 7.51 7.36 4.5 12 4.5c4.638 0 8.573 3.007 9.963 7.178.07.207.07.431 0 .639C20.577 16.49 16.64 19.5 12 19.5c-4.638 0-8.573-3.007-9.963-7.178Z"}),e("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M15 12a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z"})])}function be(s,c){return t(),n("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M21 8.25c0-2.485-2.099-4.5-4.688-4.5-1.935 0-3.597 1.126-4.312 2.733-.715-1.607-2.377-2.733-4.313-2.733C5.1 3.75 3 5.765 3 8.25c0 7.22 9 12 9 12s9-4.78 9-12Z"})])}function _e(s,c){return t(),n("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"m11.25 11.25.041-.02a.75.75 0 0 1 1.063.852l-.708 2.836a.75.75 0 0 0 1.063.853l.041-.021M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Zm-9-3.75h.008v.008H12V8.25Z"})])}function re(s,c){return t(),n("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M11.48 3.499a.562.562 0 0 1 1.04 0l2.125 5.111a.563.563 0 0 0 .475.345l5.518.442c.499.04.701.663.321.988l-4.204 3.602a.563.563 0 0 0-.182.557l1.285 5.385a.562.562 0 0 1-.84.61l-4.725-2.885a.562.562 0 0 0-.586 0L6.982 20.54a.562.562 0 0 1-.84-.61l1.285-5.386a.562.562 0 0 0-.182-.557l-4.204-3.602a.562.562 0 0 1 .321-.988l5.518-.442a.563.563 0 0 0 .475-.345L11.48 3.5Z"})])}const Fe={key:0,class:"flex items-center space-x-2"},Pe={key:1,class:"space-y-1"},Be={class:"flex items-baseline space-x-2"},Re={class:"price-text text-primary-600 dark:text-primary-400 font-bold"},Ye={key:0,class:"original-price text-gray-400 dark:text-gray-500 line-through text-sm"},Le={key:2,class:"space-y-1"},Ge={class:"flex items-baseline space-x-2"},He={class:"price-text text-purple-600 dark:text-purple-400 font-bold"},Je={class:"price-unit text-gray-500 dark:text-dark-text-secondary"},Ze={key:0,class:"original-price text-gray-400 dark:text-gray-500 line-through text-sm"},qe={class:"price-desc text-gray-500 dark:text-dark-text-secondary"},Ke={key:3,class:"space-y-1"},Qe={class:"flex items-baseline space-x-2"},We={class:"price-text text-orange-600 dark:text-orange-400 font-bold"},Xe={class:"price-unit text-gray-500 dark:text-dark-text-secondary"},et={class:"price-desc text-gray-500 dark:text-dark-text-secondary"},tt={key:4,class:"space-y-1"},at=q({__name:"PriceDisplay",props:{price:{},size:{},showDescription:{type:Boolean}},setup(s){const c=s,i=C(()=>{switch(c.size||"medium"){case"small":return"price-small";case"large":return"price-large";default:return"price-medium"}});return(r,a)=>{const u=w("el-tag");return t(),n("div",{class:F(["price-display",i.value])},[r.price.type==="free"?(t(),n("div",Fe,[a[1]||(a[1]=e("span",{class:"price-text text-green-600 dark:text-green-400 font-bold"}," 免费 ",-1)),r.size!=="small"?(t(),I(u,{key:0,type:"success",size:"small",effect:"plain"},{default:y(()=>a[0]||(a[0]=[$(" FREE ")])),_:1,__:[0]})):S("",!0)])):r.price.type==="one_time"?(t(),n("div",Pe,[e("div",Be,[e("span",Re," ¥"+l(r.price.amount),1),r.price.originalAmount&&r.price.originalAmount>r.price.amount?(t(),n("span",Ye," ¥"+l(r.price.originalAmount),1)):S("",!0)]),a[2]||(a[2]=e("div",{class:"price-desc text-gray-500 dark:text-dark-text-secondary"}," 一次购买，终身使用 ",-1)),r.price.originalAmount&&r.price.originalAmount>r.price.amount&&r.size!=="small"?(t(),I(u,{key:0,type:"danger",size:"small",effect:"plain",class:"discount-tag"},{default:y(()=>[$(l(Math.round((1-r.price.amount/r.price.originalAmount)*100))+"% OFF ",1)]),_:1})):S("",!0)])):r.price.type==="monthly"?(t(),n("div",Le,[e("div",Ge,[e("span",He," ¥"+l(r.price.amount),1),e("span",Je,l(r.price.unit||"/月"),1),r.price.originalAmount&&r.price.originalAmount>r.price.amount?(t(),n("span",Ze," ¥"+l(r.price.originalAmount),1)):S("",!0)]),e("div",qe,l(r.price.description||"按月订阅，随时取消"),1),r.price.originalAmount&&r.price.originalAmount>r.price.amount&&r.size!=="small"?(t(),I(u,{key:0,type:"warning",size:"small",effect:"plain",class:"discount-tag"},{default:y(()=>a[3]||(a[3]=[$(" 限时优惠 ")])),_:1,__:[3]})):S("",!0)])):r.price.type==="per_use"?(t(),n("div",Ke,[e("div",Qe,[e("span",We," ¥"+l(r.price.amount),1),e("span",Xe,l(r.price.unit||"/次"),1)]),e("div",et,l(r.price.description||"按使用次数计费"),1),r.size!=="small"?(t(),I(u,{key:0,type:"info",size:"small",effect:"plain"},{default:y(()=>a[4]||(a[4]=[$(" 按需付费 ")])),_:1,__:[4]})):S("",!0)])):(t(),n("div",tt,a[5]||(a[5]=[e("span",{class:"price-text text-gray-600 dark:text-gray-400"}," 价格待定 ",-1)])))],2)}}}),ce=K(at,[["__scopeId","data-v-470f89ed"]]),st={class:"relative p-6 pb-4"},rt={class:"flex items-start space-x-4"},ot={class:"flex-shrink-0"},nt={class:"w-16 h-16 bg-gradient-to-br from-primary-100 to-primary-200 dark:from-primary-900/30 dark:to-primary-800/30 rounded-xl flex items-center justify-center text-2xl font-bold border border-primary-200 dark:border-primary-700"},lt={class:"flex-1 min-w-0"},it={class:"flex items-center space-x-2 mb-1"},dt={class:"text-lg font-semibold text-gray-900 dark:text-dark-text truncate"},pt={class:"text-sm text-gray-500 dark:text-dark-text-secondary mb-2"},ct={class:"flex items-center space-x-4 text-sm"},ut={class:"flex items-center space-x-1"},mt={class:"flex items-center"},gt={class:"text-gray-600 dark:text-dark-text-secondary ml-1"},vt={class:"text-gray-500 dark:text-dark-text-secondary"},yt={class:"px-6 pb-4"},ht={class:"text-sm text-gray-600 dark:text-dark-text-secondary line-clamp-2 leading-relaxed"},kt={class:"px-6 pb-4"},xt={class:"flex flex-wrap gap-1"},ft={key:0,class:"text-xs text-gray-400 dark:text-gray-500 px-2 py-1"},bt={class:"px-6 py-4 bg-gray-50 dark:bg-dark-card/50 border-t border-gray-100 dark:border-dark-border"},_t={class:"flex items-center justify-between"},wt={class:"flex-1"},$t={class:"flex items-center space-x-2"},Ct=q({__name:"AppCard",props:{app:{}},emits:["click","favorite-toggle","install-toggle","purchase"],setup(s){const c=s,i=()=>{switch(c.app.price.type){case"free":return"安装";case"one_time":return"购买";case"monthly":return"订阅";case"per_use":return"充值";default:return"安装"}},r=a=>a>=1e4?`${(a/1e4).toFixed(1)}万`:a>=1e3?`${(a/1e3).toFixed(1)}k`:a.toString();return(a,u)=>{const T=w("el-tag"),A=w("el-button");return t(),n("div",{class:"app-card group relative bg-white dark:bg-dark-surface rounded-xl border border-gray-200 dark:border-dark-border hover:border-primary-300 dark:hover:border-primary-600 transition-all duration-300 hover:shadow-lg dark:hover:shadow-xl cursor-pointer overflow-hidden",onClick:u[3]||(u[3]=b=>a.$emit("click",a.app))},[e("button",{onClick:u[0]||(u[0]=de(b=>a.$emit("favorite-toggle",a.app.id),["stop"])),class:F(["absolute top-3 right-3 z-10 p-2 rounded-full bg-white/80 dark:bg-dark-card/80 backdrop-blur-sm border border-gray-200 dark:border-dark-border hover:bg-white dark:hover:bg-dark-card transition-all duration-200 group/fav",a.app.isFavorited?"text-red-500 hover:text-red-600":"text-gray-400 hover:text-red-500"])},[p(N(be),{class:F(["w-4 h-4 transition-transform duration-200 group-hover/fav:scale-110",a.app.isFavorited?"fill-current":""])},null,8,["class"])],2),e("div",st,[e("div",rt,[e("div",ot,[e("div",nt,l(a.app.icon),1)]),e("div",lt,[e("div",it,[e("h3",dt,l(a.app.name),1),a.app.status==="maintenance"?(t(),I(T,{key:0,type:"warning",size:"small",effect:"plain"},{default:y(()=>u[4]||(u[4]=[$(" 维护中 ")])),_:1,__:[4]})):a.app.status==="deprecated"?(t(),I(T,{key:1,type:"danger",size:"small",effect:"plain"},{default:y(()=>u[5]||(u[5]=[$(" 已废弃 ")])),_:1,__:[5]})):S("",!0)]),e("p",pt,l(a.app.developer),1),e("div",ct,[e("div",ut,[e("div",mt,[(t(),n(M,null,D(5,b=>p(N(re),{key:b,class:F(["w-4 h-4",b<=Math.floor(a.app.rating)?"text-yellow-400 fill-current":"text-gray-300 dark:text-gray-600"])},null,8,["class"])),64))]),e("span",gt,l(a.app.rating),1)]),e("div",vt,l(r(a.app.downloadCount))+" 次使用 ",1)])])])]),e("div",yt,[e("p",ht,l(a.app.description),1)]),e("div",kt,[e("div",xt,[(t(!0),n(M,null,D(a.app.tags.slice(0,3),b=>(t(),I(T,{key:b,size:"small",effect:"plain",class:"text-xs"},{default:y(()=>[$(l(b),1)]),_:2},1024))),128)),a.app.tags.length>3?(t(),n("span",ft," +"+l(a.app.tags.length-3),1)):S("",!0)])]),e("div",bt,[e("div",_t,[e("div",wt,[p(ce,{price:a.app.price,size:"small"},null,8,["price"])]),e("div",$t,[a.app.isInstalled?(t(),I(A,{key:1,onClick:u[2]||(u[2]=de(b=>a.$emit("install-toggle",a.app.id),["stop"])),type:"success",plain:"",size:"small",class:"min-w-[60px]"},{default:y(()=>u[6]||(u[6]=[$(" 已安装 ")])),_:1,__:[6]})):(t(),I(A,{key:0,onClick:u[1]||(u[1]=de(b=>a.$emit("purchase",a.app),["stop"])),type:"primary",size:"small",class:"min-w-[60px]"},{default:y(()=>[$(l(i()),1)]),_:1}))])])]),u[7]||(u[7]=e("div",{class:"absolute inset-0 bg-gradient-to-t from-primary-500/5 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300 pointer-events-none rounded-xl"},null,-1))])}}}),At=K(Ct,[["__scopeId","data-v-6f610b77"]]),Vt={class:"app-grid-container"},It={key:0,class:"loading-container"},St={class:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6"},Tt={key:1,class:"apps-container"},Nt={class:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6"},Mt={key:0,class:"pagination-container mt-8 flex justify-center"},Dt={key:2,class:"empty-state"},zt={class:"text-center py-16"},Ot={class:"text-lg font-medium text-gray-900 dark:text-dark-text mb-2"},jt={class:"text-gray-500 dark:text-dark-text-secondary max-w-md mx-auto"},Ut={class:"mt-6"},Et=q({__name:"AppGrid",props:{apps:{},loading:{type:Boolean},emptyTitle:{},emptyDescription:{}},emits:["app-click","favorite-toggle","install-toggle","purchase","clear-filters"],setup(s){const c=s,i=k(1),r=k(12),a=C(()=>Math.ceil(c.apps.length/r.value)),u=C(()=>{const f=(i.value-1)*r.value,x=f+r.value;return c.apps.slice(f,x)}),T=C(()=>c.emptyTitle||"暂无应用"),A=C(()=>c.emptyDescription||"当前筛选条件下没有找到相关应用，请尝试调整筛选条件或搜索关键词。"),b=f=>{r.value=f,i.value=1},j=f=>{i.value=f,window.scrollTo({top:0,behavior:"smooth"})};return Ve(()=>c.apps.length,()=>{i.value=1}),(f,x)=>{const U=w("el-pagination"),L=w("el-button"),h=w("el-backtop");return t(),n("div",Vt,[f.loading?(t(),n("div",It,[e("div",St,[(t(),n(M,null,D(8,d=>e("div",{key:d,class:"app-card-skeleton bg-white dark:bg-dark-surface rounded-xl border border-gray-200 dark:border-dark-border overflow-hidden"},x[6]||(x[6]=[fe('<div class="p-6 space-y-4" data-v-05dc4bdd><div class="flex items-start space-x-4" data-v-05dc4bdd><div class="w-16 h-16 bg-gray-200 dark:bg-gray-700 rounded-xl animate-pulse" data-v-05dc4bdd></div><div class="flex-1 space-y-2" data-v-05dc4bdd><div class="h-5 bg-gray-200 dark:bg-gray-700 rounded animate-pulse" data-v-05dc4bdd></div><div class="h-4 bg-gray-200 dark:bg-gray-700 rounded w-3/4 animate-pulse" data-v-05dc4bdd></div></div></div><div class="space-y-2" data-v-05dc4bdd><div class="h-4 bg-gray-200 dark:bg-gray-700 rounded animate-pulse" data-v-05dc4bdd></div><div class="h-4 bg-gray-200 dark:bg-gray-700 rounded w-5/6 animate-pulse" data-v-05dc4bdd></div></div><div class="flex space-x-2" data-v-05dc4bdd><div class="h-6 w-16 bg-gray-200 dark:bg-gray-700 rounded animate-pulse" data-v-05dc4bdd></div><div class="h-6 w-12 bg-gray-200 dark:bg-gray-700 rounded animate-pulse" data-v-05dc4bdd></div></div></div><div class="px-6 py-4 bg-gray-50 dark:bg-dark-card/50 border-t border-gray-100 dark:border-dark-border" data-v-05dc4bdd><div class="flex items-center justify-between" data-v-05dc4bdd><div class="h-6 w-20 bg-gray-200 dark:bg-gray-700 rounded animate-pulse" data-v-05dc4bdd></div><div class="h-8 w-16 bg-gray-200 dark:bg-gray-700 rounded animate-pulse" data-v-05dc4bdd></div></div></div>',2)]))),64))])])):f.apps.length>0?(t(),n("div",Tt,[e("div",Nt,[(t(!0),n(M,null,D(u.value,d=>(t(),I(At,{key:d.id,app:d,onClick:o=>f.$emit("app-click",d),onFavoriteToggle:x[0]||(x[0]=o=>f.$emit("favorite-toggle",o)),onInstallToggle:x[1]||(x[1]=o=>f.$emit("install-toggle",o)),onPurchase:x[2]||(x[2]=o=>f.$emit("purchase",o))},null,8,["app","onClick"]))),128))]),a.value>1?(t(),n("div",Mt,[p(U,{"current-page":i.value,"onUpdate:currentPage":x[3]||(x[3]=d=>i.value=d),"page-size":r.value,"onUpdate:pageSize":x[4]||(x[4]=d=>r.value=d),"page-sizes":[12,24,36,48],total:f.apps.length,layout:"total, sizes, prev, pager, next, jumper",onSizeChange:b,onCurrentChange:j,class:"modern-pagination"},null,8,["current-page","page-size","total"])])):S("",!0)])):(t(),n("div",Dt,[e("div",zt,[x[8]||(x[8]=e("div",{class:"w-24 h-24 mx-auto mb-6 bg-gray-100 dark:bg-gray-800 rounded-full flex items-center justify-center"},[e("svg",{class:"w-12 h-12 text-gray-400 dark:text-gray-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M20 13V6a2 2 0 00-2-2H6a2 2 0 00-2 2v7m16 0v5a2 2 0 01-2 2H6a2 2 0 01-2-2v-5m16 0h-2M4 13h2m13-8V4a1 1 0 00-1-1H7a1 1 0 00-1 1v1m8 0V4.5"})])],-1)),e("h3",Ot,l(T.value),1),e("p",jt,l(A.value),1),e("div",Ut,[p(L,{onClick:x[5]||(x[5]=d=>f.$emit("clear-filters")),type:"primary",plain:""},{default:y(()=>x[7]||(x[7]=[$(" 清除筛选条件 ")])),_:1,__:[7]})])])])),p(h,{right:40,bottom:40,"visibility-height":300})])}}}),ye=K(Et,[["__scopeId","data-v-05dc4bdd"]]),Ft={key:0,class:"purchase-content"},Pt={class:"app-info flex items-center space-x-4 mb-6 p-4 bg-gray-50 dark:bg-dark-card rounded-lg"},Bt={class:"w-16 h-16 bg-gradient-to-br from-primary-100 to-primary-200 dark:from-primary-900/30 dark:to-primary-800/30 rounded-xl flex items-center justify-center text-2xl font-bold border border-primary-200 dark:border-primary-700"},Rt={class:"flex-1"},Yt={class:"text-lg font-semibold text-gray-900 dark:text-dark-text"},Lt={class:"text-sm text-gray-600 dark:text-dark-text-secondary"},Gt={class:"price-section mb-6"},Ht={class:"bg-white dark:bg-dark-surface border border-gray-200 dark:border-dark-border rounded-lg p-4"},Jt={key:0,class:"mt-4 p-3 bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg"},Zt={class:"flex items-center space-x-2"},qt={key:1,class:"mt-4 space-y-2"},Kt={class:"flex items-center justify-between text-sm"},Qt={class:"font-medium text-gray-900 dark:text-dark-text"},Wt={key:0,class:"flex items-center justify-between text-sm"},Xt={class:"text-gray-400 line-through"},ea={class:"flex items-center justify-between text-sm font-medium border-t border-gray-200 dark:border-dark-border pt-2"},ta={class:"text-lg text-primary-600 dark:text-primary-400"},aa={key:2,class:"mt-4 space-y-3"},sa={class:"p-3 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg"},ra={class:"flex items-center space-x-2 mb-2"},oa={class:"flex items-center justify-between text-sm font-medium border-t border-gray-200 dark:border-dark-border pt-2"},na={class:"text-lg text-primary-600 dark:text-primary-400"},la={key:3,class:"mt-4 space-y-3"},ia={class:"p-3 bg-orange-50 dark:bg-orange-900/20 border border-orange-200 dark:border-orange-800 rounded-lg"},da={class:"flex items-center space-x-2 mb-2"},pa={class:"space-y-2"},ca={class:"flex items-center justify-between text-sm"},ua={class:"font-medium text-gray-900 dark:text-dark-text"},ma={class:"flex items-center justify-between text-sm"},ga={class:"flex items-center justify-between text-sm"},va={class:"font-medium text-gray-900 dark:text-dark-text"},ya={key:0,class:"payment-section mb-6"},ha={class:"space-y-2"},ka=["value"],xa={class:"flex items-center space-x-3 flex-1"},fa={class:"text-2xl"},ba={class:"font-medium text-gray-900 dark:text-dark-text"},_a={class:"text-sm text-gray-500 dark:text-dark-text-secondary"},wa={key:0,class:"text-primary-600 dark:text-primary-400"},$a={key:1,class:"agreement-section mb-6"},Ca={class:"flex items-start space-x-3 cursor-pointer"},Aa={class:"flex justify-end space-x-3"},Va=q({__name:"PurchaseDialog",props:{modelValue:{type:Boolean},app:{}},emits:["update:modelValue","purchase-success"],setup(s,{emit:c}){const i=s,r=c,a=k(!1),u=k("alipay"),T=k(!1),A=k(50),b=C({get:()=>i.modelValue,set:h=>r("update:modelValue",h)}),j=C(()=>i.app?i.app.price.type==="free"?!0:T.value&&u.value:!1),f=k([{id:"alipay",name:"支付宝",icon:"💙",description:"推荐使用，安全快捷"},{id:"wechat",name:"微信支付",icon:"💚",description:"微信扫码支付"},{id:"unionpay",name:"银联支付",icon:"💳",description:"银行卡支付"}]),x=()=>{if(!i.app)return"购买应用";switch(i.app.price.type){case"free":return"安装免费应用";case"one_time":return"购买应用";case"monthly":return"订阅应用";case"per_use":return"充值使用";default:return"购买应用"}},U=()=>{if(!i.app)return"确认";switch(i.app.price.type){case"free":return"立即安装";case"one_time":return`立即购买 ¥${i.app.price.amount}`;case"monthly":return`立即订阅 ¥${i.app.price.amount}/月`;case"per_use":return`充值 ¥${A.value}`;default:return"确认购买"}},L=async()=>{if(!(!i.app||!j.value)){a.value=!0;try{await new Promise(h=>setTimeout(h,2e3)),i.app.price.type==="free"?X.success(`${i.app.name} 安装成功！`):X.success(`${i.app.name} 购买成功！`),r("purchase-success",i.app.id),b.value=!1}catch{X.error("操作失败，请重试")}finally{a.value=!1}}};return(h,d)=>{const o=w("el-input-number"),v=w("el-checkbox"),P=w("el-button"),G=w("el-dialog");return t(),I(G,{modelValue:b.value,"onUpdate:modelValue":d[4]||(d[4]=V=>b.value=V),title:x(),width:"500px","align-center":"","close-on-click-modal":!1,class:"purchase-dialog"},{footer:y(()=>[e("div",Aa,[p(P,{onClick:d[3]||(d[3]=V=>b.value=!1),size:"large"},{default:y(()=>d[20]||(d[20]=[$(" 取消 ")])),_:1,__:[20]}),p(P,{onClick:L,type:"primary",size:"large",loading:a.value,disabled:!j.value},{default:y(()=>[$(l(U()),1)]),_:1},8,["loading","disabled"])])]),default:y(()=>[h.app?(t(),n("div",Ft,[e("div",Pt,[e("div",Bt,l(h.app.icon),1),e("div",Rt,[e("h3",Yt,l(h.app.name),1),e("p",Lt,l(h.app.developer),1)])]),e("div",Gt,[d[17]||(d[17]=e("h4",{class:"text-base font-medium text-gray-900 dark:text-dark-text mb-3"}," 价格详情 ",-1)),e("div",Ht,[p(ce,{price:h.app.price,size:"large"},null,8,["price"]),h.app.price.type==="free"?(t(),n("div",Jt,[e("div",Zt,[p(N(ve),{class:"w-5 h-5 text-green-600 dark:text-green-400"}),d[5]||(d[5]=e("span",{class:"text-sm text-green-800 dark:text-green-300"}," 此应用完全免费，无需付费即可使用所有功能 ",-1))])])):h.app.price.type==="one_time"?(t(),n("div",qt,[e("div",Kt,[d[6]||(d[6]=e("span",{class:"text-gray-600 dark:text-dark-text-secondary"},"应用价格",-1)),e("span",Qt,"¥"+l(h.app.price.amount),1)]),h.app.price.originalAmount&&h.app.price.originalAmount>h.app.price.amount?(t(),n("div",Wt,[d[7]||(d[7]=e("span",{class:"text-gray-600 dark:text-dark-text-secondary"},"原价",-1)),e("span",Xt,"¥"+l(h.app.price.originalAmount),1)])):S("",!0),e("div",ea,[d[8]||(d[8]=e("span",{class:"text-gray-900 dark:text-dark-text"},"总计",-1)),e("span",ta,"¥"+l(h.app.price.amount),1)])])):h.app.price.type==="monthly"?(t(),n("div",aa,[e("div",sa,[e("div",ra,[p(N(_e),{class:"w-5 h-5 text-blue-600 dark:text-blue-400"}),d[9]||(d[9]=e("span",{class:"text-sm font-medium text-blue-800 dark:text-blue-300"},"订阅说明",-1))]),d[10]||(d[10]=e("ul",{class:"text-sm text-blue-700 dark:text-blue-300 space-y-1"},[e("li",null,"• 按月自动续费，可随时取消"),e("li",null,"• 享受所有高级功能"),e("li",null,"• 优先技术支持"),e("li",null,"• 无使用次数限制")],-1))]),e("div",oa,[d[11]||(d[11]=e("span",{class:"text-gray-900 dark:text-dark-text"},"月费",-1)),e("span",na,"¥"+l(h.app.price.amount)+"/月",1)])])):h.app.price.type==="per_use"?(t(),n("div",la,[e("div",ia,[e("div",da,[p(N(Ue),{class:"w-5 h-5 text-orange-600 dark:text-orange-400"}),d[12]||(d[12]=e("span",{class:"text-sm font-medium text-orange-800 dark:text-orange-300"},"按需付费",-1))]),d[13]||(d[13]=e("p",{class:"text-sm text-orange-700 dark:text-orange-300"}," 根据实际使用次数计费，用多少付多少，经济实惠 ",-1))]),e("div",pa,[e("div",ca,[d[14]||(d[14]=e("span",{class:"text-gray-600 dark:text-dark-text-secondary"},"单次使用费用",-1)),e("span",ua,"¥"+l(h.app.price.amount),1)]),e("div",ma,[d[15]||(d[15]=e("span",{class:"text-gray-600 dark:text-dark-text-secondary"},"预充值金额",-1)),p(o,{modelValue:A.value,"onUpdate:modelValue":d[0]||(d[0]=V=>A.value=V),min:10,max:1e3,step:10,size:"small",style:{width:"120px"}},null,8,["modelValue"])]),e("div",ga,[d[16]||(d[16]=e("span",{class:"text-gray-600 dark:text-dark-text-secondary"},"可使用次数",-1)),e("span",va," 约 "+l(Math.floor(A.value/h.app.price.amount))+" 次 ",1)])])])):S("",!0)])]),h.app.price.type!=="free"?(t(),n("div",ya,[d[18]||(d[18]=e("h4",{class:"text-base font-medium text-gray-900 dark:text-dark-text mb-3"}," 支付方式 ",-1)),e("div",ha,[(t(!0),n(M,null,D(f.value,V=>(t(),n("label",{key:V.id,class:F(["flex items-center p-3 border border-gray-200 dark:border-dark-border rounded-lg cursor-pointer hover:border-primary-300 dark:hover:border-primary-600 transition-colors",u.value===V.id?"border-primary-500 bg-primary-50 dark:bg-primary-900/20":""])},[Ie(e("input",{"onUpdate:modelValue":d[1]||(d[1]=Q=>u.value=Q),value:V.id,type:"radio",class:"sr-only"},null,8,ka),[[Se,u.value]]),e("div",xa,[e("div",fa,l(V.icon),1),e("div",null,[e("div",ba,l(V.name),1),e("div",_a,l(V.description),1)])]),u.value===V.id?(t(),n("div",wa,[p(N(ve),{class:"w-5 h-5"})])):S("",!0)],2))),128))])])):S("",!0),h.app.price.type!=="free"?(t(),n("div",$a,[e("label",Ca,[p(v,{modelValue:T.value,"onUpdate:modelValue":d[2]||(d[2]=V=>T.value=V)},null,8,["modelValue"]),d[19]||(d[19]=e("span",{class:"text-sm text-gray-600 dark:text-dark-text-secondary"},[$(" 我已阅读并同意 "),e("a",{href:"#",class:"text-primary-600 dark:text-primary-400 hover:underline"},"《用户服务协议》"),$(" 和 "),e("a",{href:"#",class:"text-primary-600 dark:text-primary-400 hover:underline"},"《隐私政策》")],-1))])])):S("",!0)])):S("",!0)]),_:1},8,["modelValue","title"])}}}),we=K(Va,[["__scopeId","data-v-2b0e2073"]]),Ia={key:0,class:"app-details-content"},Sa={class:"app-header flex items-start space-x-6 mb-6"},Ta={class:"flex-shrink-0"},Na={class:"w-20 h-20 bg-gradient-to-br from-primary-100 to-primary-200 dark:from-primary-900/30 dark:to-primary-800/30 rounded-2xl flex items-center justify-center text-3xl font-bold border border-primary-200 dark:border-primary-700"},Ma={class:"flex-1 min-w-0"},Da={class:"flex items-center space-x-3 mb-2"},za={class:"text-2xl font-bold text-gray-900 dark:text-dark-text"},Oa={class:"text-gray-600 dark:text-dark-text-secondary mb-3"},ja={class:"flex items-center space-x-6 text-sm mb-4"},Ua={class:"flex items-center space-x-2"},Ea={class:"flex items-center"},Fa={class:"font-medium text-gray-900 dark:text-dark-text"},Pa={class:"text-gray-500 dark:text-dark-text-secondary"},Ba={class:"text-gray-500 dark:text-dark-text-secondary"},Ra={class:"text-gray-500 dark:text-dark-text-secondary"},Ya={class:"flex flex-wrap gap-2"},La={class:"flex-shrink-0 space-y-3"},Ga={class:"text-right"},Ha={class:"flex flex-col space-y-2"},Ja={class:"space-y-6"},Za={class:"text-gray-600 dark:text-dark-text-secondary leading-relaxed"},qa={key:0},Ka={class:"space-y-2"},Qa={class:"text-gray-600 dark:text-dark-text-secondary"},Wa={key:1},Xa={class:"space-y-2"},es={class:"text-gray-600 dark:text-dark-text-secondary"},ts={key:0,class:"space-y-4"},as={class:"grid grid-cols-1 md:grid-cols-2 gap-4"},ss=["onClick"],rs=["src","alt"],os={class:"absolute inset-0 bg-black/0 group-hover:bg-black/10 transition-colors duration-200 rounded-lg flex items-center justify-center"},ns={key:1,class:"text-center py-12"},ls={class:"space-y-6"},is={class:"bg-gray-50 dark:bg-dark-card rounded-lg p-6"},ds={class:"flex items-center space-x-8"},ps={class:"text-center"},cs={class:"text-3xl font-bold text-gray-900 dark:text-dark-text"},us={class:"flex items-center justify-center mt-1"},ms={class:"text-sm text-gray-500 dark:text-dark-text-secondary mt-1"},gs={class:"flex-1 space-y-2"},vs={class:"text-sm text-gray-600 dark:text-dark-text-secondary w-8"},ys={class:"flex-1 bg-gray-200 dark:bg-gray-700 rounded-full h-2"},hs={class:"text-sm text-gray-500 dark:text-dark-text-secondary w-8"},ks={class:"space-y-4"},xs={class:"flex items-start space-x-4"},fs={class:"w-10 h-10 bg-gradient-to-br from-blue-400 to-purple-500 rounded-full flex items-center justify-center text-white font-medium"},bs={class:"flex-1"},_s={class:"flex items-center space-x-3 mb-2"},ws={class:"font-medium text-gray-900 dark:text-dark-text"},$s={class:"flex items-center"},Cs={class:"text-sm text-gray-500 dark:text-dark-text-secondary"},As={class:"text-gray-600 dark:text-dark-text-secondary leading-relaxed"},Vs=q({__name:"AppDetailsDialog",props:{modelValue:{type:Boolean},app:{}},emits:["update:modelValue","favorite-toggle","install-toggle"],setup(s,{emit:c}){const i=s,r=c,a=k("overview"),u=k(!1),T=k(0),A=k(!1),b=C({get:()=>i.modelValue,set:o=>r("update:modelValue",o)}),j=k([{id:1,user:"张三",rating:5,date:"2024-01-15",content:"非常好用的工具，界面简洁，功能强大，大大提高了工作效率！"},{id:2,user:"李四",rating:4,date:"2024-01-12",content:"整体不错，就是有些功能还需要完善，期待后续更新。"},{id:3,user:"王五",rating:5,date:"2024-01-10",content:"强烈推荐！客服响应也很及时，解决问题很专业。"}]),f=o=>o>=1e4?`${(o/1e4).toFixed(1)}万`:o>=1e3?`${(o/1e3).toFixed(1)}k`:o.toString(),x=o=>new Date(o).toLocaleDateString("zh-CN"),U=o=>({5:65,4:20,3:10,2:3,1:2})[o]||0,L=()=>{if(!i.app)return"立即安装";switch(i.app.price.type){case"free":return"立即安装";case"one_time":return`立即购买 ¥${i.app.price.amount}`;case"monthly":return`立即订阅 ¥${i.app.price.amount}/月`;case"per_use":return"立即充值";default:return"立即安装"}},h=(o,v)=>{T.value=v,u.value=!0},d=o=>{r("install-toggle",o),A.value=!1};return(o,v)=>{var te;const P=w("el-tag"),G=w("el-button"),V=w("el-tab-pane"),Q=w("el-tabs"),oe=w("el-image-viewer"),ne=w("el-dialog");return t(),I(ne,{modelValue:b.value,"onUpdate:modelValue":v[6]||(v[6]=J=>b.value=J),title:((te=o.app)==null?void 0:te.name)||"应用详情",width:"900px","align-center":"","close-on-click-modal":!1,class:"app-details-dialog"},{default:y(()=>{var J;return[o.app?(t(),n("div",Ia,[e("div",Sa,[e("div",Ta,[e("div",Na,l(o.app.icon),1)]),e("div",Ma,[e("div",Da,[e("h2",za,l(o.app.name),1),o.app.status==="maintenance"?(t(),I(P,{key:0,type:"warning",effect:"plain"},{default:y(()=>v[7]||(v[7]=[$(" 维护中 ")])),_:1,__:[7]})):o.app.status==="deprecated"?(t(),I(P,{key:1,type:"danger",effect:"plain"},{default:y(()=>v[8]||(v[8]=[$(" 已废弃 ")])),_:1,__:[8]})):S("",!0)]),e("p",Oa,l(o.app.developer)+" • v"+l(o.app.version),1),e("div",ja,[e("div",Ua,[e("div",Ea,[(t(),n(M,null,D(5,m=>p(N(re),{key:m,class:F(["w-5 h-5",m<=Math.floor(o.app.rating)?"text-yellow-400 fill-current":"text-gray-300 dark:text-gray-600"])},null,8,["class"])),64))]),e("span",Fa,l(o.app.rating),1),e("span",Pa," ("+l(o.app.reviewCount)+" 评价) ",1)]),e("div",Ba,l(f(o.app.downloadCount))+" 次使用 ",1),e("div",Ra," 更新于 "+l(x(o.app.lastUpdated)),1)]),e("div",Ya,[(t(!0),n(M,null,D(o.app.tags,m=>(t(),I(P,{key:m,size:"small",effect:"plain"},{default:y(()=>[$(l(m),1)]),_:2},1024))),128))])]),e("div",La,[e("div",Ga,[p(ce,{price:o.app.price,size:"large"},null,8,["price"])]),e("div",Ha,[o.app.isInstalled?(t(),I(G,{key:1,onClick:v[1]||(v[1]=m=>o.$emit("install-toggle",o.app.id)),type:"success",plain:"",size:"large",class:"w-full"},{default:y(()=>v[9]||(v[9]=[$(" 已安装 ")])),_:1,__:[9]})):(t(),I(G,{key:0,onClick:v[0]||(v[0]=m=>A.value=!0),type:"primary",size:"large",class:"w-full"},{default:y(()=>[$(l(L()),1)]),_:1})),p(G,{onClick:v[2]||(v[2]=m=>o.$emit("favorite-toggle",o.app.id)),type:o.app.isFavorited?"danger":"default",plain:!o.app.isFavorited,size:"large",class:"w-full"},{default:y(()=>[p(N(be),{class:F(["w-4 h-4 mr-2",o.app.isFavorited?"fill-current":""])},null,8,["class"]),$(" "+l(o.app.isFavorited?"已收藏":"收藏"),1)]),_:1},8,["type","plain"])])])]),p(Q,{modelValue:a.value,"onUpdate:modelValue":v[3]||(v[3]=m=>a.value=m),class:"app-details-tabs"},{default:y(()=>[p(V,{label:"应用介绍",name:"overview"},{default:y(()=>[e("div",Ja,[e("div",null,[v[10]||(v[10]=e("h3",{class:"text-lg font-semibold text-gray-900 dark:text-dark-text mb-3"}," 应用简介 ",-1)),e("p",Za,l(o.app.longDescription||o.app.description),1)]),o.app.features&&o.app.features.length>0?(t(),n("div",qa,[v[11]||(v[11]=e("h3",{class:"text-lg font-semibold text-gray-900 dark:text-dark-text mb-3"}," 主要功能 ",-1)),e("ul",Ka,[(t(!0),n(M,null,D(o.app.features,m=>(t(),n("li",{key:m,class:"flex items-center space-x-3"},[p(N(je),{class:"w-5 h-5 text-green-500 flex-shrink-0"}),e("span",Qa,l(m),1)]))),128))])])):S("",!0),o.app.requirements&&o.app.requirements.length>0?(t(),n("div",Wa,[v[12]||(v[12]=e("h3",{class:"text-lg font-semibold text-gray-900 dark:text-dark-text mb-3"}," 使用要求 ",-1)),e("ul",Xa,[(t(!0),n(M,null,D(o.app.requirements,m=>(t(),n("li",{key:m,class:"flex items-center space-x-3"},[p(N(_e),{class:"w-5 h-5 text-blue-500 flex-shrink-0"}),e("span",es,l(m),1)]))),128))])])):S("",!0)])]),_:1}),p(V,{label:"应用截图",name:"screenshots"},{default:y(()=>[o.app.screenshots&&o.app.screenshots.length>0?(t(),n("div",ts,[e("div",as,[(t(!0),n(M,null,D(o.app.screenshots,(m,B)=>(t(),n("div",{key:B,class:"relative group cursor-pointer",onClick:le=>h(m,B)},[e("img",{src:m,alt:`应用截图 ${B+1}`,class:"w-full h-48 object-cover rounded-lg border border-gray-200 dark:border-dark-border group-hover:border-primary-300 dark:group-hover:border-primary-600 transition-colors duration-200"},null,8,rs),e("div",os,[p(N(Ee),{class:"w-8 h-8 text-white opacity-0 group-hover:opacity-100 transition-opacity duration-200"})])],8,ss))),128))])])):(t(),n("div",ns,[p(N(Te),{class:"w-16 h-16 text-gray-300 dark:text-gray-600 mx-auto mb-4"}),v[13]||(v[13]=e("p",{class:"text-gray-500 dark:text-dark-text-secondary"},"暂无应用截图",-1))]))]),_:1}),p(V,{label:"用户评价",name:"reviews"},{default:y(()=>[e("div",ls,[e("div",is,[e("div",ds,[e("div",ps,[e("div",cs,l(o.app.rating),1),e("div",us,[(t(),n(M,null,D(5,m=>p(N(re),{key:m,class:F(["w-4 h-4",m<=Math.floor(o.app.rating)?"text-yellow-400 fill-current":"text-gray-300 dark:text-gray-600"])},null,8,["class"])),64))]),e("div",ms,l(o.app.reviewCount)+" 条评价 ",1)]),e("div",gs,[(t(),n(M,null,D([5,4,3,2,1],m=>e("div",{key:m,class:"flex items-center space-x-3"},[e("span",vs,l(m)+"星 ",1),e("div",ys,[e("div",{class:"bg-yellow-400 h-2 rounded-full",style:Ne({width:`${U(m)}%`})},null,4)]),e("span",hs,l(U(m))+"% ",1)])),64))])])]),e("div",ks,[(t(!0),n(M,null,D(j.value,m=>(t(),n("div",{key:m.id,class:"border border-gray-200 dark:border-dark-border rounded-lg p-4"},[e("div",xs,[e("div",fs,l(m.user.charAt(0)),1),e("div",bs,[e("div",_s,[e("span",ws,l(m.user),1),e("div",$s,[(t(),n(M,null,D(5,B=>p(N(re),{key:B,class:F(["w-4 h-4",B<=m.rating?"text-yellow-400 fill-current":"text-gray-300 dark:text-gray-600"])},null,8,["class"])),64))]),e("span",Cs,l(m.date),1)]),e("p",As,l(m.content),1)])])]))),128))])])]),_:1})]),_:1},8,["modelValue"])])):S("",!0),u.value?(t(),I(oe,{key:1,"url-list":((J=o.app)==null?void 0:J.screenshots)||[],"initial-index":T.value,onClose:v[4]||(v[4]=m=>u.value=!1)},null,8,["url-list","initial-index"])):S("",!0),p(we,{modelValue:A.value,"onUpdate:modelValue":v[5]||(v[5]=m=>A.value=m),app:o.app,onPurchaseSuccess:d},null,8,["modelValue","app"])]}),_:1},8,["modelValue","title"])}}}),Is=K(Vs,[["__scopeId","data-v-5098ad69"]]);var W=(s=>(s.FREE="free",s.ONE_TIME="one_time",s.MONTHLY="monthly",s.PER_USE="per_use",s))(W||{}),R=(s=>(s.IMAGE_PROCESSING="image_processing",s.DATA_ANALYSIS="data_analysis",s.SEO_TOOLS="seo_tools",s.MARKET_ANALYSIS="market_analysis",s.MANAGEMENT_TOOLS="management_tools",s.AUTOMATION="automation",s.CONTENT_CREATION="content_creation",s))(R||{});const Y=k([]),Z=k([]),H=k([]),pe=k(!1),ee=k({}),Ss=[{id:"product-collection",name:"商品采集",description:"智能采集全球电商平台商品信息，支持Amazon、Temu、Shein等主流平台",longDescription:"商品采集是一款专业的电商数据采集工具，支持从Amazon、Temu、Shein等主流电商平台批量采集商品信息。具备智能去重、数据清洗、格式转换等功能，是电商从业者的必备工具。",icon:"🛒",screenshots:["https://picsum.photos/800/600?random=1","https://picsum.photos/800/600?random=2","https://picsum.photos/800/600?random=3"],category:"data_analysis",tags:["数据采集","电商","批量处理","多平台"],price:{type:"monthly",amount:49.9,currency:"CNY",originalAmount:69.9,unit:"/月",description:"专业版功能，支持无限采集"},rating:4.9,reviewCount:2341,downloadCount:28750,developer:"RiinAI团队",version:"3.2.1",lastUpdated:"2024-01-16",status:"active",features:["多平台支持","智能去重","数据清洗","批量导出","定时采集"],requirements:["需要网络连接","支持Chrome浏览器插件"]},{id:"smart-crop",name:"智能裁图",description:"AI智能图片裁剪和优化，自动识别主体，支持多种裁剪比例",longDescription:"智能裁图是一款基于深度学习的图片裁剪工具，能够自动识别图片主体，进行智能裁剪。支持批量处理、多种裁剪比例、自定义裁剪区域等功能。",icon:"✂️",screenshots:["https://picsum.photos/800/600?random=4","https://picsum.photos/800/600?random=5","https://picsum.photos/800/600?random=6"],category:"image_processing",tags:["AI","图片处理","批量处理","智能裁剪"],price:{type:"monthly",amount:29.9,currency:"CNY",originalAmount:39.9,unit:"/月",description:"包含所有高级功能，无限制使用"},rating:4.8,reviewCount:1856,downloadCount:22420,developer:"RiinAI团队",version:"2.1.0",lastUpdated:"2024-01-15",status:"active",features:["智能主体识别","批量处理支持","多种裁剪比例","高质量输出","云端处理"],requirements:["需要网络连接","支持JPG/PNG格式"]},{id:"one-click-cutout",name:"一键抠图",description:"一键智能抠图，去除背景，AI驱动的背景移除工具",longDescription:"一键抠图使用先进的AI算法，能够精确识别图片主体，自动移除背景。支持批量处理，输出高质量透明背景图片，是设计师和电商从业者的得力助手。",icon:"🎨",screenshots:["https://picsum.photos/800/600?random=7","https://picsum.photos/800/600?random=8"],category:"image_processing",tags:["AI","抠图","背景移除","图片处理"],price:{type:"per_use",amount:.5,currency:"CNY",unit:"/张",description:"按使用次数计费，高质量处理"},rating:4.9,reviewCount:3241,downloadCount:45750,developer:"RiinAI团队",version:"1.8.0",lastUpdated:"2024-01-14",status:"active",features:["AI智能识别","高精度抠图","批量处理","多格式支持","云端处理"]},{id:"super-split",name:"超级裂变",description:"营销裂变工具，快速传播，支持多种裂变模式和数据分析",longDescription:"超级裂变是一款专业的营销裂变工具，支持多种裂变模式，包括分享裂变、任务裂变、拼团裂变等。提供详细的数据分析和用户行为追踪，帮助企业快速扩大用户规模。",icon:"🚀",screenshots:["https://picsum.photos/800/600?random=9","https://picsum.photos/800/600?random=10","https://picsum.photos/800/600?random=11"],category:"automation",tags:["营销","裂变","传播","数据分析"],price:{type:"monthly",amount:199,currency:"CNY",unit:"/月",description:"专业营销工具，支持无限裂变活动"},rating:4.7,reviewCount:1567,downloadCount:18920,developer:"RiinAI团队",version:"2.5.0",lastUpdated:"2024-01-13",status:"active",features:["多种裂变模式","数据分析","用户追踪","活动管理","效果统计"]},{id:"title-generator",name:"标题生成",description:"AI智能生成吸引人的标题，支持多种模板和平台优化",longDescription:"AI标题生成器基于大语言模型，能够为不同平台生成吸引人的标题。支持电商、自媒体、广告等多种场景，提供模板库和个性化定制功能。",icon:"📝",screenshots:["https://picsum.photos/800/600?random=12","https://picsum.photos/800/600?random=13"],category:"content_creation",tags:["AI","标题生成","内容创作","营销"],price:{type:"per_use",amount:.1,currency:"CNY",unit:"/次",description:"按生成次数计费，经济实惠"},rating:4.6,reviewCount:2890,downloadCount:35670,developer:"RiinAI团队",version:"1.9.2",lastUpdated:"2024-01-12",status:"active",features:["AI智能生成","多平台优化","模板库","批量生成","效果预测"]},{id:"pod-compose",name:"POD合成",description:"按需印刷商品合成工具，支持图案与产品的智能合成",longDescription:"POD合成工具专为按需印刷业务设计，支持将设计图案与各种产品模板进行智能合成。提供丰富的产品库、智能排版、批量处理等功能。",icon:"🎨",screenshots:["https://picsum.photos/800/600?random=14","https://picsum.photos/800/600?random=15","https://picsum.photos/800/600?random=16"],category:"image_processing",tags:["POD","合成","印刷","设计"],price:{type:"monthly",amount:89,currency:"CNY",unit:"/月",description:"专业POD工具，支持无限合成"},rating:4.5,reviewCount:1234,downloadCount:12450,developer:"RiinAI团队",version:"1.6.0",lastUpdated:"2024-01-11",status:"active",features:["智能合成","产品模板库","批量处理","高清输出","自动排版"]},{id:"batch-listing",name:"批量刊登",description:"批量发布商品到各大平台，支持模板导入和自动化发布",longDescription:"批量刊登工具支持将商品信息批量发布到Amazon、eBay、Shopify等多个电商平台。提供模板导入、数据映射、自动化发布等功能，大大提高运营效率。",icon:"📦",screenshots:["https://picsum.photos/800/600?random=17","https://picsum.photos/800/600?random=18"],category:"automation",tags:["批量发布","电商","自动化","多平台"],price:{type:"monthly",amount:159,currency:"CNY",unit:"/月",description:"专业电商工具，支持无限发布"},rating:4.4,reviewCount:987,downloadCount:15680,developer:"RiinAI团队",version:"2.3.1",lastUpdated:"2024-01-10",status:"active",features:["多平台支持","模板导入","批量发布","数据映射","发布监控"]},{id:"price-monitor",name:"价格监控",description:"实时监控商品价格变化，支持多平台价格对比和降价提醒",longDescription:"价格监控工具帮助您实时跟踪商品价格变化，支持Amazon、Temu、Shein等主流电商平台。提供价格历史图表、降价提醒、竞品对比等功能。",icon:"📊",screenshots:["https://picsum.photos/800/600?random=19","https://picsum.photos/800/600?random=20"],category:"data_analysis",tags:["价格监控","数据分析","电商","提醒"],price:{type:"free",amount:0,currency:"CNY",description:"免费版本，每日可监控10个商品"},rating:4.2,reviewCount:1892,downloadCount:28750,developer:"第三方开发者",version:"1.5.2",lastUpdated:"2024-01-10",status:"active",features:["多平台支持","价格历史图表","降价提醒","数据导出"],requirements:["需要网络连接"]},{id:"keyword-research",name:"关键词研究",description:"专业的关键词挖掘和分析工具，助力SEO优化",longDescription:"关键词研究专家是一款专业的SEO工具，提供关键词挖掘、竞争度分析、搜索量预测等功能。帮助您找到高价值的关键词，提升网站排名。",icon:"🔍",screenshots:["https://picsum.photos/800/600?random=21","https://picsum.photos/800/600?random=22","https://picsum.photos/800/600?random=23"],category:"seo_tools",tags:["SEO","关键词","搜索优化","竞争分析"],price:{type:"one_time",amount:199,currency:"CNY",originalAmount:299,description:"一次购买，终身使用"},rating:4.6,reviewCount:1567,downloadCount:13240,developer:"SEO专家团队",version:"3.0.1",lastUpdated:"2024-01-12",status:"active",features:["关键词挖掘","竞争度分析","搜索量预测","长尾词推荐","数据报告"]},{id:"review-analyzer",name:"评论分析",description:"智能分析商品评论情感，提取用户反馈洞察",longDescription:"评论分析工具使用自然语言处理技术，智能分析商品评论的情感倾向、关键词提取、用户满意度等。帮助商家了解产品优缺点，优化产品和服务。",icon:"💬",screenshots:["https://picsum.photos/800/600?random=24","https://picsum.photos/800/600?random=25"],category:"data_analysis",tags:["评论分析","NLP","情感分析","用户洞察"],price:{type:"monthly",amount:79,currency:"CNY",unit:"/月",description:"专业分析工具，无限制使用"},rating:4.3,reviewCount:756,downloadCount:9870,developer:"AI分析团队",version:"2.1.0",lastUpdated:"2024-01-09",status:"active",features:["情感分析","关键词提取","满意度评分","趋势分析","报告生成"]},{id:"competitor-analysis",name:"竞品分析",description:"深度分析竞争对手策略，洞察市场机会",longDescription:"竞品分析大师帮助您深入了解竞争对手的产品策略、价格策略、营销手段等。提供详细的分析报告和市场洞察，助力商业决策。",icon:"🎯",screenshots:["https://picsum.photos/800/600?random=26","https://picsum.photos/800/600?random=27"],category:"market_analysis",tags:["竞品分析","市场研究","策略分析","商业智能"],price:{type:"monthly",amount:299,currency:"CNY",unit:"/月",description:"专业版功能，深度分析报告"},rating:4.4,reviewCount:623,downloadCount:7890,developer:"商业分析专家",version:"2.3.0",lastUpdated:"2024-01-08",status:"active",features:["竞品监控","价格对比","营销策略分析","市场趋势预测","定制报告"]},{id:"inventory-management",name:"库存管理",description:"智能库存管理系统，支持多仓库、多渠道库存同步",longDescription:"智能库存管理系统提供全面的库存控制功能，支持多仓库管理、库存预警、自动补货、销售预测等。帮助企业优化库存结构，降低运营成本。",icon:"📋",screenshots:["https://picsum.photos/800/600?random=28","https://picsum.photos/800/600?random=29","https://picsum.photos/800/600?random=30"],category:"management_tools",tags:["库存管理","仓储","供应链","预测"],price:{type:"monthly",amount:399,currency:"CNY",unit:"/月",description:"企业级库存管理解决方案"},rating:4.5,reviewCount:445,downloadCount:5670,developer:"企业管理专家",version:"3.1.2",lastUpdated:"2024-01-07",status:"active",features:["多仓库管理","库存预警","自动补货","销售预测","报表分析"]},{id:"customer-service",name:"客服助手",description:"AI智能客服机器人，24小时自动回复客户咨询",longDescription:"AI客服助手基于大语言模型，能够理解客户问题并提供准确回复。支持多平台接入、知识库管理、人工客服转接等功能，大幅提升客服效率。",icon:"🎧",screenshots:["https://picsum.photos/800/600?random=31","https://picsum.photos/800/600?random=32"],category:"automation",tags:["AI客服","自动回复","知识库","多平台"],price:{type:"monthly",amount:199,currency:"CNY",unit:"/月",description:"AI客服解决方案，支持无限对话"},rating:4.6,reviewCount:1234,downloadCount:15670,developer:"AI服务团队",version:"2.0.5",lastUpdated:"2024-01-06",status:"active",features:["AI智能回复","多平台接入","知识库管理","人工转接","对话分析"]},{id:"data-export",name:"数据导出",description:"多格式数据导出工具，支持Excel、CSV、JSON等格式",longDescription:"数据导出工具支持将各种业务数据导出为多种格式，包括Excel、CSV、JSON、PDF等。提供数据清洗、格式转换、定时导出等功能。",icon:"📤",screenshots:["https://picsum.photos/800/600?random=33","https://picsum.photos/800/600?random=34"],category:"data_analysis",tags:["数据导出","格式转换","数据清洗","自动化"],price:{type:"free",amount:0,currency:"CNY",description:"免费工具，基础导出功能"},rating:4.1,reviewCount:567,downloadCount:12340,developer:"数据工具团队",version:"1.4.0",lastUpdated:"2024-01-05",status:"active",features:["多格式支持","数据清洗","批量导出","定时任务","模板定制"]},{id:"report-generator",name:"报表生成",description:"自动生成业务报表，支持多种图表和数据可视化",longDescription:"报表生成器能够自动收集业务数据，生成专业的分析报表。支持多种图表类型、数据可视化、定时发送等功能，让数据分析更简单。",icon:"📈",screenshots:["https://picsum.photos/800/600?random=35","https://picsum.photos/800/600?random=36","https://picsum.photos/800/600?random=37"],category:"data_analysis",tags:["报表生成","数据可视化","图表","自动化"],price:{type:"monthly",amount:129,currency:"CNY",unit:"/月",description:"专业报表工具，无限制生成"},rating:4.7,reviewCount:890,downloadCount:11230,developer:"数据分析专家",version:"2.2.1",lastUpdated:"2024-01-04",status:"active",features:["自动数据收集","多种图表","数据可视化","定时发送","模板库"]},{id:"workflow-automation",name:"工作流自动化",description:"无代码工作流自动化平台，连接各种应用和服务",longDescription:"工作流自动化平台让您无需编程即可创建复杂的自动化流程。支持连接数百种应用和服务，实现数据同步、任务自动化、通知提醒等功能。",icon:"⚡",screenshots:["https://picsum.photos/800/600?random=38","https://picsum.photos/800/600?random=39","https://picsum.photos/800/600?random=40"],category:"automation",tags:["工作流","自动化","无代码","集成"],price:{type:"monthly",amount:299,currency:"CNY",originalAmount:399,unit:"/月",description:"企业级自动化解决方案"},rating:4.8,reviewCount:1567,downloadCount:8900,developer:"自动化专家",version:"3.0.0",lastUpdated:"2024-01-03",status:"active",features:["无代码设计","应用集成","条件触发","数据转换","监控告警"]}],Ts=()=>{Y.value=Ss.map(s=>({...s,isFavorited:Z.value.includes(s.id),isInstalled:H.value.includes(s.id)}))},Ns=()=>Y.value,he=C(()=>Y.value.filter(s=>s.isFavorited)),Ms=C(()=>{let s=Y.value;const c=ee.value;if(c.searchKeyword){const i=c.searchKeyword.toLowerCase();s=s.filter(r=>r.name.toLowerCase().includes(i)||r.description.toLowerCase().includes(i)||r.tags.some(a=>a.toLowerCase().includes(i)))}return c.category&&(s=s.filter(i=>i.category===c.category)),c.priceType&&(s=s.filter(i=>i.price.type===c.priceType)),c.rating!==void 0&&(s=s.filter(i=>i.rating>=c.rating)),c.sortBy&&s.sort((i,r)=>{let a,u;switch(c.sortBy){case"name":a=i.name,u=r.name;break;case"rating":a=i.rating,u=r.rating;break;case"downloadCount":a=i.downloadCount,u=r.downloadCount;break;case"lastUpdated":a=new Date(i.lastUpdated),u=new Date(r.lastUpdated);break;case"price":a=i.price.amount,u=r.price.amount;break;default:return 0}return c.sortOrder==="desc"?a>u?-1:a<u?1:0:a<u?-1:a>u?1:0}),s}),Ds=s=>{ee.value={...ee.value,...s}},zs=()=>{ee.value={}},Os=s=>{const c=Y.value.find(r=>r.id===s);if(!c)return!1;const i=Z.value.indexOf(s);return i>-1?(Z.value.splice(i,1),c.isFavorited=!1):(Z.value.push(s),c.isFavorited=!0),localStorage.setItem("app-market-favorites",JSON.stringify(Z.value)),c.isFavorited},js=s=>{const c=Y.value.find(r=>r.id===s);if(!c)return!1;const i=H.value.indexOf(s);return i>-1?(H.value.splice(i,1),c.isInstalled=!1):(H.value.push(s),c.isInstalled=!0),localStorage.setItem("app-market-installed",JSON.stringify(H.value)),c.isInstalled},ke=s=>Y.value.find(c=>c.id===s),Us=()=>{pe.value=!0;const s=localStorage.getItem("app-market-favorites");s&&(Z.value=JSON.parse(s));const c=localStorage.getItem("app-market-installed");c&&(H.value=JSON.parse(c)),Ts(),pe.value=!1},xe={apps:C(()=>Y.value),installedApps:H,loading:C(()=>pe.value),currentFilter:C(()=>ee.value)},Es={class:"space-y-6"},Fs={class:"grid grid-cols-1 md:grid-cols-3 gap-6"},Ps={class:"bg-white dark:bg-dark-surface rounded-xl p-6 shadow-elegant dark:shadow-elegant-dark border border-gray-100 dark:border-dark-border hover:shadow-lg dark:hover:shadow-2xl transition-all duration-300"},Bs={class:"flex items-center justify-between"},Rs={class:"text-2xl font-bold text-gray-900 dark:text-dark-text"},Ys={class:"bg-white dark:bg-dark-surface rounded-xl p-6 shadow-elegant dark:shadow-elegant-dark border border-gray-100 dark:border-dark-border hover:shadow-lg dark:hover:shadow-2xl transition-all duration-300"},Ls={class:"flex items-center justify-between"},Gs={class:"text-2xl font-bold text-green-600 dark:text-green-400"},Hs={class:"bg-white dark:bg-dark-surface rounded-xl p-6 shadow-elegant dark:shadow-elegant-dark border border-gray-100 dark:border-dark-border hover:shadow-lg dark:hover:shadow-2xl transition-all duration-300"},Js={class:"flex items-center justify-between"},Zs={class:"text-2xl font-bold text-blue-600 dark:text-blue-400"},qs={class:"bg-white dark:bg-dark-surface rounded-xl p-6 shadow-elegant dark:shadow-elegant-dark border border-gray-100 dark:border-dark-border"},Ks={class:"flex flex-col lg:flex-row lg:items-center lg:justify-between space-y-4 lg:space-y-0 lg:space-x-4"},Qs={class:"flex-1 max-w-md"},Ws={class:"flex items-center space-x-4"},Xs={class:"bg-white dark:bg-dark-surface rounded-xl shadow-elegant dark:shadow-elegant-dark border border-gray-100 dark:border-dark-border overflow-hidden"},er={class:"flex items-center"},tr=q({__name:"index",setup(s){const c=k("all"),i=k(""),r=k(""),a=k(""),u=k(""),T=k("name"),A=k("asc"),b=k(!1),j=k(null),f=k(!1),x=k(null),U=C(()=>xe.loading.value),L=C(()=>Ns().length),h=C(()=>he.value.length),d=C(()=>xe.installedApps.value.length),o=C(()=>Ms.value),v=C(()=>he.value),P=C(()=>[{label:"图像处理",value:R.IMAGE_PROCESSING},{label:"数据分析",value:R.DATA_ANALYSIS},{label:"SEO工具",value:R.SEO_TOOLS},{label:"市场分析",value:R.MARKET_ANALYSIS},{label:"管理工具",value:R.MANAGEMENT_TOOLS},{label:"自动化工具",value:R.AUTOMATION},{label:"内容创作",value:R.CONTENT_CREATION}]),G=C(()=>[{label:"免费",value:W.FREE},{label:"一口价",value:W.ONE_TIME},{label:"包月",value:W.MONTHLY},{label:"按次计费",value:W.PER_USE}]),V=()=>{m()},Q=()=>{m()},oe=()=>{m()},ne=()=>{m()},te=()=>{m()},J=()=>{A.value=A.value==="asc"?"desc":"asc",m()},m=()=>{const z={searchKeyword:i.value||void 0,category:r.value||void 0,priceType:a.value||void 0,rating:u.value||void 0,sortBy:T.value,sortOrder:A.value};Ds(z)},B=()=>{i.value="",r.value="",a.value="",u.value="",T.value="name",A.value="asc",zs()},le=z=>{j.value=z,b.value=!0},ie=z=>{const g=Os(z),E=ke(z);E&&X.success(g?`已收藏 ${E.name}`:`已取消收藏 ${E.name}`)},ae=z=>{const g=js(z),E=ke(z);E&&X.success(g?`已安装 ${E.name}`:`已卸载 ${E.name}`)},ue=z=>{x.value=z,f.value=!0},$e=z=>{ae(z),f.value=!1};return Me(()=>{Us()}),(z,g)=>{const E=w("el-input"),O=w("el-option"),se=w("el-select"),me=w("el-button"),ge=w("el-tab-pane"),Ce=w("el-badge"),Ae=w("el-tabs");return t(),n("div",Es,[g[16]||(g[16]=fe('<div class="bg-gradient-to-r from-primary-50 to-blue-50 dark:from-primary-900/20 dark:to-blue-900/20 rounded-2xl p-6 border border-primary-100 dark:border-primary-800" data-v-b3ae7454><div class="flex items-center space-x-3" data-v-b3ae7454><div class="w-10 h-10 bg-gradient-to-br from-primary-500 to-primary-600 rounded-xl flex items-center justify-center" data-v-b3ae7454><svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24" data-v-b3ae7454><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" data-v-b3ae7454></path></svg></div><div data-v-b3ae7454><h1 class="text-2xl font-bold text-gray-900 dark:text-dark-text" data-v-b3ae7454>应用市场</h1><p class="mt-1 text-sm text-gray-600 dark:text-dark-text-secondary" data-v-b3ae7454>发现和管理您的应用工具</p></div></div></div>',1)),e("div",Fs,[e("div",Ps,[e("div",Bs,[e("div",null,[g[8]||(g[8]=e("p",{class:"text-sm font-medium text-gray-600 dark:text-dark-text-secondary"},"总应用数",-1)),e("p",Rs,l(L.value),1)]),g[9]||(g[9]=e("div",{class:"w-12 h-12 bg-primary-100 dark:bg-primary-900/30 rounded-lg flex items-center justify-center"},[e("svg",{class:"w-6 h-6 text-primary-600 dark:text-primary-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"})])],-1))])]),e("div",Ys,[e("div",Ls,[e("div",null,[g[10]||(g[10]=e("p",{class:"text-sm font-medium text-gray-600 dark:text-dark-text-secondary"},"我的收藏",-1)),e("p",Gs,l(h.value),1)]),g[11]||(g[11]=e("div",{class:"w-12 h-12 bg-green-100 dark:bg-green-900/30 rounded-lg flex items-center justify-center"},[e("svg",{class:"w-6 h-6 text-green-600 dark:text-green-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"})])],-1))])]),e("div",Hs,[e("div",Js,[e("div",null,[g[12]||(g[12]=e("p",{class:"text-sm font-medium text-gray-600 dark:text-dark-text-secondary"},"已安装",-1)),e("p",Zs,l(d.value),1)]),g[13]||(g[13]=e("div",{class:"w-12 h-12 bg-blue-100 dark:bg-blue-900/30 rounded-lg flex items-center justify-center"},[e("svg",{class:"w-6 h-6 text-blue-600 dark:text-blue-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"})])],-1))])])]),e("div",qs,[e("div",Ks,[e("div",Qs,[p(E,{modelValue:i.value,"onUpdate:modelValue":g[0]||(g[0]=_=>i.value=_),placeholder:"搜索应用名称、描述或标签...",onInput:V,clearable:""},{prefix:y(()=>[p(N(De),{class:"w-4 h-4 text-gray-400"})]),_:1},8,["modelValue"])]),e("div",Ws,[p(se,{modelValue:r.value,"onUpdate:modelValue":g[1]||(g[1]=_=>r.value=_),placeholder:"选择分类",clearable:"",style:{width:"140px"},onChange:Q},{default:y(()=>[p(O,{label:"全部分类",value:""}),(t(!0),n(M,null,D(P.value,_=>(t(),I(O,{key:_.value,label:_.label,value:_.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"]),p(se,{modelValue:a.value,"onUpdate:modelValue":g[2]||(g[2]=_=>a.value=_),placeholder:"价格类型",clearable:"",style:{width:"120px"},onChange:oe},{default:y(()=>[p(O,{label:"全部价格",value:""}),(t(!0),n(M,null,D(G.value,_=>(t(),I(O,{key:_.value,label:_.label,value:_.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"]),p(se,{modelValue:u.value,"onUpdate:modelValue":g[3]||(g[3]=_=>u.value=_),placeholder:"最低评分",clearable:"",style:{width:"120px"},onChange:ne},{default:y(()=>[p(O,{label:"全部评分",value:""}),p(O,{label:"4星以上",value:4}),p(O,{label:"3星以上",value:3}),p(O,{label:"2星以上",value:2})]),_:1},8,["modelValue"]),p(se,{modelValue:T.value,"onUpdate:modelValue":g[4]||(g[4]=_=>T.value=_),placeholder:"排序方式",style:{width:"140px"},onChange:te},{default:y(()=>[p(O,{label:"按名称排序",value:"name"}),p(O,{label:"按评分排序",value:"rating"}),p(O,{label:"按下载量排序",value:"downloadCount"}),p(O,{label:"按更新时间排序",value:"lastUpdated"}),p(O,{label:"按价格排序",value:"price"})]),_:1},8,["modelValue"]),p(me,{onClick:J,icon:A.value==="asc"?N(Oe):N(ze),circle:"",size:"default"},null,8,["icon"]),p(me,{onClick:B,type:"info",plain:""},{default:y(()=>g[14]||(g[14]=[$(" 清除筛选 ")])),_:1,__:[14]})])])]),e("div",Xs,[p(Ae,{modelValue:c.value,"onUpdate:modelValue":g[5]||(g[5]=_=>c.value=_),class:"app-market-tabs"},{default:y(()=>[p(ge,{label:"全部应用",name:"all"},{default:y(()=>[p(ye,{apps:o.value,loading:U.value,onAppClick:le,onFavoriteToggle:ie,onInstallToggle:ae,onPurchase:ue},null,8,["apps","loading"])]),_:1}),p(ge,{name:"favorites"},{label:y(()=>[e("span",er,[g[15]||(g[15]=$(" 我的收藏 ")),p(Ce,{value:h.value,hidden:h.value===0,class:"ml-2"},null,8,["value","hidden"])])]),default:y(()=>[p(ye,{apps:v.value,loading:U.value,onAppClick:le,onFavoriteToggle:ie,onInstallToggle:ae,onPurchase:ue},null,8,["apps","loading"])]),_:1})]),_:1},8,["modelValue"])]),p(Is,{modelValue:b.value,"onUpdate:modelValue":g[6]||(g[6]=_=>b.value=_),app:j.value,onFavoriteToggle:ie,onInstallToggle:ae},null,8,["modelValue","app"]),p(we,{modelValue:f.value,"onUpdate:modelValue":g[7]||(g[7]=_=>f.value=_),app:x.value,onPurchaseSuccess:$e},null,8,["modelValue","app"])])}}}),rr=K(tr,[["__scopeId","data-v-b3ae7454"]]);export{rr as default};
