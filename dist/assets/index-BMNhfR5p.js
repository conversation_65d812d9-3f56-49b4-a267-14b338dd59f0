import{d as q,f as E,r as x,h as O,j as l,k as w,a as e,l as s,B as $,o as k,E as f,_ as J,c as h,F as A,m as K,n as X,t as d,b as ee,D as te,G as ae,H as le,A as Q,K as de,p as ne}from"./index-g0Lcbgij.js";import{_ as ie,r as Z}from"./GallerySelectDialog.vue_vue_type_script_setup_true_lang-C52m7-ZF.js";const ue={class:"space-y-6"},ce={class:"space-y-2"},me={class:"space-y-2"},ge={class:"space-y-2"},xe={class:"flex justify-end space-x-3"},pe=q({__name:"CreatePresetDialog",props:{modelValue:{type:Boolean}},emits:["update:modelValue","success"],setup(P,{emit:z}){const D=P,V=z,C=E({get:()=>D.modelValue,set:j=>V("update:modelValue",j)}),m=x({name:"",description:"",rules:""}),M=()=>{if(!m.value.name.trim()){f.warning("请输入预设名称");return}if(!m.value.rules.trim()){f.warning("请输入生成规则");return}const j={id:Date.now().toString(),name:m.value.name.trim(),description:m.value.description.trim(),rules:m.value.rules.trim()};V("success",j),V("update:modelValue",!1),L()},L=()=>{m.value={name:"",description:"",rules:""}};return(j,u)=>{const a=w("el-input"),y=w("el-button"),S=w("el-dialog");return k(),O(S,{modelValue:C.value,"onUpdate:modelValue":u[4]||(u[4]=v=>C.value=v),title:"新建预设",width:"600px","align-center":"",onClose:L},{footer:l(()=>[e("div",xe,[s(y,{onClick:u[3]||(u[3]=v=>C.value=!1)},{default:l(()=>u[9]||(u[9]=[$("取消")])),_:1,__:[9]}),s(y,{type:"primary",onClick:M,disabled:!m.value.name.trim()||!m.value.rules.trim()},{default:l(()=>u[10]||(u[10]=[$(" 创建预设 ")])),_:1,__:[10]},8,["disabled"])])]),default:l(()=>[e("div",ue,[e("div",ce,[u[5]||(u[5]=e("label",{class:"block text-sm font-medium text-gray-900 dark:text-dark-text"},[$(" 预设名称 "),e("span",{class:"text-red-500"},"*")],-1)),s(a,{modelValue:m.value.name,"onUpdate:modelValue":u[0]||(u[0]=v=>m.value.name=v),placeholder:"请输入预设名称，如：亚马逊标题",class:"modern-input"},null,8,["modelValue"])]),e("div",me,[u[6]||(u[6]=e("label",{class:"block text-sm font-medium text-gray-900 dark:text-dark-text"}," 预设描述 ",-1)),s(a,{modelValue:m.value.description,"onUpdate:modelValue":u[1]||(u[1]=v=>m.value.description=v),placeholder:"请输入预设描述，简要说明此预设的用途和特点",class:"modern-input"},null,8,["modelValue"])]),e("div",ge,[u[7]||(u[7]=e("label",{class:"block text-sm font-medium text-gray-900 dark:text-dark-text"},[$(" 生成规则 "),e("span",{class:"text-red-500"},"*")],-1)),s(a,{modelValue:m.value.rules,"onUpdate:modelValue":u[2]||(u[2]=v=>m.value.rules=v),type:"textarea",rows:8,placeholder:"请输入详细的生成规则...",class:"modern-textarea"},null,8,["modelValue"]),u[8]||(u[8]=e("div",{class:"mt-3 p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg border border-blue-200 dark:border-blue-800"},[e("h4",{class:"text-sm font-medium text-blue-900 dark:text-blue-100 mb-2"},"预设示例："),e("div",{class:"text-sm text-blue-800 dark:text-blue-200 space-y-2"},[e("p",null,[e("strong",null,"【亚马逊标题】")]),e("p",null,"任务：根据图片内容拟定标题；"),e("p",null,"要求：商品款式+商品颜色+印花图案+使用场景，遵循<亚马逊>平台要求，输出<英文>，总字符量不超过120个字符")])],-1))])])]),_:1},8,["modelValue"])}}}),ke=J(pe,[["__scopeId","data-v-34a6ef34"]]),ve={class:"space-y-6"},be={class:"flex justify-start space-x-3"},he={class:"bg-gray-50 dark:bg-dark-card rounded-lg p-4"},fe={class:"flex items-center justify-between mb-3"},we={class:"relative"},ye={key:0,class:"absolute right-0 top-8 w-80 bg-white dark:bg-dark-surface border border-gray-200 dark:border-dark-border rounded-lg shadow-lg z-10"},_e={class:"p-4"},Ce={class:"flex items-center justify-between mb-3"},$e={class:"space-y-2 max-h-48 overflow-y-auto"},Ve=["onClick"],je={class:"font-medium text-sm text-gray-900 dark:text-dark-text"},Te={class:"text-xs text-gray-500 dark:text-dark-text-secondary mt-1 line-clamp-2"},Me={key:0,class:"space-y-4"},Se={class:"flex items-center justify-between"},Be={class:"text-sm font-medium text-gray-900 dark:text-dark-text"},ze={class:"text-xs text-gray-500 dark:text-dark-text-secondary"},De={class:"border border-gray-200 dark:border-dark-border rounded-lg p-4 max-h-80 overflow-y-auto"},Le={class:"grid grid-cols-6 gap-4"},Pe={class:"relative"},He=["src","alt"],Ge=["onClick"],Ue={class:"text-xs text-gray-600 dark:text-dark-text-secondary mt-1 truncate"},Ie={key:0,class:"mt-4 flex justify-center"},Ne={key:1,class:"text-center py-12 border-2 border-dashed border-gray-200 dark:border-dark-border rounded-lg"},Fe={class:"flex justify-between items-center"},Re={class:"text-sm text-gray-500 dark:text-dark-text-secondary"},We={class:"flex space-x-3"},Ae=q({__name:"CreateTitleDialog",props:{modelValue:{type:Boolean}},emits:["update:modelValue","success"],setup(P,{emit:z}){const D=P,V=z,C=E({get:()=>D.modelValue,set:g=>V("update:modelValue",g)}),m=x(!1),M=x(!1),L=x(!1),j=x(!1),u=x([]),a=x([]),y=x(1),S=x(18),v=x(""),H=x([{id:"1",name:"亚马逊标题",description:"根据图片内容拟定标题；要求：商品款式+商品颜色+印花图案+使用场景，遵循亚马逊平台要求，输出英文，总字符量不超过120个字符",rules:"任务：根据图片内容拟定标题；要求：商品款式+商品颜色+印花图案+使用场景，遵循<亚马逊>平台要求，输出<英文>，总字符量不超过120个字符"},{id:"2",name:"Temu标题",description:"根据图片内容生成Temu平台标题；要求：突出产品特色和卖点，中英文混合，字符数80-100",rules:"任务：根据图片内容拟定标题；要求：突出产品特色和卖点，遵循<Temu>平台要求，输出<中英文混合>，总字符量80-100个字符"},{id:"3",name:"Shein标题",description:"根据图片内容生成Shein平台标题；要求：时尚感强，突出风格和场合，英文输出，60-80字符",rules:"任务：根据图片内容拟定标题；要求：时尚感强，突出风格和场合，遵循<Shein>平台要求，输出<英文>，总字符量60-80个字符"}]),b=E(()=>{const g=(y.value-1)*S.value,r=g+S.value;return a.value.slice(g,r)}),o=g=>{const r=new FileReader;r.onload=T=>{var n;const R=Date.now()+Math.random();a.value.push({id:R,name:g.name,url:(n=T.target)==null?void 0:n.result,file:g.raw})},r.readAsDataURL(g.raw)},B=g=>{},G=g=>{a.value.splice(g,1),b.value.length===0&&y.value>1&&y.value--},U=g=>{const r=g.map(T=>({id:T.id,name:T.name,url:T.url}));a.value.push(...r),m.value=!1,f.success(`已添加 ${g.length} 张图片`)},I=g=>{v.value=g.rules,M.value=!1,f.success(`已应用预设：${g.name}`)},N=g=>{H.value.push(g),f.success("预设创建成功！")},F=()=>{if(a.value.length===0){f.warning("请先选择图片");return}if(!v.value.trim()){f.warning("请设置生成规则");return}f.success(`正在创建标题生成任务，共 ${a.value.length} 张图片`),p(),V("success"),V("update:modelValue",!1)},p=()=>{a.value=[],u.value=[],y.value=1,v.value="",M.value=!1};return(g,r)=>{const T=w("el-button"),R=w("el-upload"),n=w("el-input"),t=w("el-pagination"),_=w("el-dialog");return k(),h(A,null,[s(_,{modelValue:C.value,"onUpdate:modelValue":r[8]||(r[8]=i=>C.value=i),title:"新建标题生成任务",width:"900px","align-center":"",onClose:p},{footer:l(()=>[e("div",Fe,[e("div",Re,d(a.value.length>0?`将处理 ${a.value.length} 张图片，生成 ${a.value.length*3} 个标题`:"请先选择图片和设置生成规则"),1),e("div",We,[s(T,{onClick:r[7]||(r[7]=i=>C.value=!1)},{default:l(()=>r[18]||(r[18]=[$("取消")])),_:1,__:[18]}),s(T,{type:"primary",onClick:F,disabled:a.value.length===0||!v.value.trim()},{default:l(()=>r[19]||(r[19]=[$(" 提交任务 ")])),_:1,__:[19]},8,["disabled"])])])]),default:l(()=>[e("div",ve,[e("div",be,[s(R,{ref:"uploadRef","file-list":u.value,"on-change":o,"on-remove":B,"auto-upload":!1,multiple:"",accept:"image/*","show-file-list":!1},{default:l(()=>[s(T,{type:"primary",size:"large"},{default:l(()=>r[11]||(r[11]=[e("svg",{class:"w-5 h-5 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"})],-1),$(" 上传图片 ")])),_:1,__:[11]})]),_:1},8,["file-list"]),s(T,{size:"large",onClick:r[0]||(r[0]=i=>m.value=!0)},{default:l(()=>r[12]||(r[12]=[e("svg",{class:"w-5 h-5 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"})],-1),$(" 从图库选择 ")])),_:1,__:[12]})]),e("div",he,[e("div",fe,[r[14]||(r[14]=e("h4",{class:"text-sm font-medium text-gray-900 dark:text-dark-text"},"生成规则设置",-1)),e("div",we,[e("button",{onMouseenter:r[1]||(r[1]=i=>L.value=!0),onMouseleave:r[2]||(r[2]=i=>L.value=!1),onClick:r[3]||(r[3]=i=>M.value=!M.value),class:"text-sm text-blue-600 dark:text-blue-400 hover:text-blue-700 dark:hover:text-blue-300 transition-colors duration-200"}," 使用预设 ",32),M.value?(k(),h("div",ye,[e("div",_e,[e("div",Ce,[r[13]||(r[13]=e("h5",{class:"text-sm font-medium text-gray-900 dark:text-dark-text"},"预设列表",-1)),e("button",{onClick:r[4]||(r[4]=i=>j.value=!0),class:"text-xs text-blue-600 dark:text-blue-400 hover:text-blue-700"}," 新建预设 ")]),e("div",$e,[(k(!0),h(A,null,X(H.value,i=>(k(),h("div",{key:i.id,onClick:W=>I(i),class:"p-3 border border-gray-200 dark:border-dark-border rounded-lg cursor-pointer hover:bg-gray-50 dark:hover:bg-dark-card transition-colors duration-200"},[e("div",je,d(i.name),1),e("div",Te,d(i.description),1)],8,Ve))),128))])])])):K("",!0)])]),s(n,{modelValue:v.value,"onUpdate:modelValue":r[5]||(r[5]=i=>v.value=i),type:"textarea",rows:6,placeholder:"请输入生成规则，例如：根据图片内容拟定标题；要求：商品款式+商品颜色+印花图案+使用场景，遵循亚马逊平台要求，输出英文，总字符量不超过120个字符",class:"modern-textarea"},null,8,["modelValue"])]),a.value.length>0?(k(),h("div",Me,[e("div",Se,[e("h4",Be," 已选择图片 ("+d(a.value.length)+") ",1),e("div",ze," 预计生成 "+d(a.value.length*3)+" 个标题 ",1)]),e("div",De,[e("div",Le,[(k(!0),h(A,null,X(b.value,(i,W)=>(k(),h("div",{key:i.id||W,class:"relative group"},[e("div",Pe,[e("img",{src:i.url,alt:i.name,class:"w-full h-20 object-cover rounded-lg border border-gray-200 dark:border-dark-border"},null,8,He),e("button",{onClick:Y=>G(W+(y.value-1)*S.value),class:"absolute -top-2 -right-2 w-6 h-6 bg-red-500 text-white rounded-full flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity duration-200 hover:bg-red-600"},r[15]||(r[15]=[e("svg",{class:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M6 18L18 6M6 6l12 12"})],-1)]),8,Ge),r[16]||(r[16]=e("div",{class:"absolute bottom-1 right-1 bg-blue-500 text-white text-xs px-1.5 py-0.5 rounded"}," ×3 ",-1))]),e("p",Ue,d(i.name),1)]))),128))]),a.value.length>S.value?(k(),h("div",Ie,[s(t,{"current-page":y.value,"onUpdate:currentPage":r[6]||(r[6]=i=>y.value=i),"page-size":S.value,total:a.value.length,layout:"prev, pager, next",small:""},null,8,["current-page","page-size","total"])])):K("",!0)])])):(k(),h("div",Ne,r[17]||(r[17]=[e("svg",{class:"w-12 h-12 text-gray-400 dark:text-dark-text-secondary mx-auto mb-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"})],-1),e("p",{class:"text-gray-500 dark:text-dark-text-secondary"},"请上传图片或从图库选择",-1)])))])]),_:1},8,["modelValue"]),s(ie,{modelValue:m.value,"onUpdate:modelValue":r[9]||(r[9]=i=>m.value=i),"theme-color":"blue",onSelect:U},null,8,["modelValue"]),s(ke,{modelValue:j.value,"onUpdate:modelValue":r[10]||(r[10]=i=>j.value=i),onSuccess:N},null,8,["modelValue"])],64)}}}),Ee=J(Ae,[["__scopeId","data-v-75f92d91"]]),Oe={class:"flex items-center justify-between p-6 border-b border-gray-100 dark:border-dark-border"},Ke={class:"flex items-center space-x-3"},qe={class:"text-sm text-gray-600 dark:text-dark-text-secondary"},Je={key:0,class:"p-6 grid grid-cols-2 md:grid-cols-4 gap-4"},Qe={class:"bg-gradient-to-br from-blue-50 to-blue-100 dark:from-blue-900/20 dark:to-blue-800/20 p-4 rounded-xl border border-blue-200 dark:border-blue-800"},Xe={class:"flex items-center space-x-2"},Ye={class:"text-sm font-bold text-blue-900 dark:text-blue-100"},Ze={class:"bg-gradient-to-br from-green-50 to-green-100 dark:from-green-900/20 dark:to-green-800/20 p-4 rounded-xl border border-green-200 dark:border-green-800"},et={class:"flex items-center space-x-2"},tt={class:"text-sm font-bold text-green-900 dark:text-green-100"},rt={class:"bg-gradient-to-br from-indigo-50 to-indigo-100 dark:from-indigo-900/20 dark:to-indigo-800/20 p-4 rounded-xl border border-indigo-200 dark:border-indigo-800"},st={class:"flex items-center space-x-2"},ot={class:"text-sm font-bold text-indigo-900 dark:text-indigo-100"},at={class:"bg-gradient-to-br from-cyan-50 to-cyan-100 dark:from-cyan-900/20 dark:to-cyan-800/20 p-4 rounded-xl border border-cyan-200 dark:border-cyan-800"},lt={class:"flex items-center space-x-2"},dt={class:"text-sm font-bold text-cyan-900 dark:text-cyan-100"},nt={class:"px-6 pb-4"},it={class:"bg-gray-50 dark:bg-dark-card/50 p-4 rounded-lg border border-gray-100 dark:border-dark-border"},ut={class:"text-sm text-gray-700 dark:text-dark-text-secondary whitespace-pre-line"},ct={class:"px-6 pb-6"},mt={class:"bg-white dark:bg-dark-surface rounded-xl border border-gray-200 dark:border-dark-border overflow-hidden"},gt={key:0,class:"flex justify-center"},xt={key:1,class:"flex justify-center"},pt={class:"text-sm text-gray-600 dark:text-dark-text-secondary"},kt={key:0,class:"space-y-2"},vt={class:"flex items-center justify-between"},bt={class:"text-sm text-gray-900 dark:text-dark-text"},ht=["onClick"],ft={key:1,class:"text-sm text-gray-500 dark:text-dark-text-secondary"},wt={key:0,class:"flex justify-center"},yt=["onClick"],_t={key:1,class:"flex justify-center"},Ct={class:"flex items-center justify-between p-6 border-t border-gray-100 dark:border-dark-border bg-gray-50 dark:bg-dark-card/50"},$t={class:"text-sm text-gray-600 dark:text-dark-text-secondary"},Vt=q({__name:"ViewDetailsDialog",props:{modelValue:{type:Boolean},task:{}},emits:["update:modelValue"],setup(P,{emit:z}){const D=P,V=z,C=E({get:()=>D.modelValue,set:b=>V("update:modelValue",b)}),m=x(!1),M=x(0),L=x("任务：根据图片内容拟定标题；要求：商品款式+商品颜色+印花图案+使用场景，遵循<亚马逊>平台要求，输出<英文>，总字符量不超过120个字符"),j=x([{index:1,image:"https://picsum.photos/400/400?random=1",fileName:"product_001.jpg",titles:["Women's Floral Print Summer Dress - Blue Casual Maxi Dress with Pockets for Beach Vacation","Elegant Blue Floral Maxi Dress for Women - Casual Summer Beach Vacation Outfit with Side Pockets","Summer Beach Maxi Dress - Women's Blue Floral Print Casual Outfit with Pockets for Vacation"],status:"success"},{index:2,image:"https://picsum.photos/400/400?random=2",fileName:"product_002.jpg",titles:["Men's Casual Linen Shirt - White Button Down Short Sleeve Beach Shirt for Summer Vacation","Summer White Linen Shirt for Men - Casual Short Sleeve Button Down Beach Wear for Vacation","Men's White Short Sleeve Linen Shirt - Casual Button Down Beach Wear for Summer Vacation"],status:"success"},{index:3,image:"https://picsum.photos/400/400?random=3",fileName:"product_003.jpg",titles:[],status:"failed"}]);M.value=j.value.length;const u=b=>({completed:"已完成",processing:"处理中",failed:"失败",pending:"等待中"})[b]||"未知",a=b=>({success:"成功",failed:"失败"})[b]||"未知",y=()=>{C.value=!1},S=b=>{navigator.clipboard.writeText(b).then(()=>{f.success("标题已复制到剪贴板")}).catch(()=>{f.error("复制失败，请手动复制")})},v=b=>{f.success(`正在导出 ${b.fileName} 的标题`)},H=()=>{f.success("正在导出所有标题...")};return(b,o)=>{const B=w("el-table-column"),G=w("el-image"),U=w("el-tag"),I=w("el-table"),N=w("el-dialog"),F=te("loading");return k(),O(N,{modelValue:C.value,"onUpdate:modelValue":o[0]||(o[0]=p=>C.value=p),width:"1200px","before-close":y,"show-close":!1,class:"modern-dialog"},{header:l(()=>{var p;return[e("div",Oe,[e("div",Ke,[o[2]||(o[2]=e("div",{class:"w-10 h-10 bg-gradient-to-br from-blue-500 to-blue-600 rounded-xl flex items-center justify-center"},[e("svg",{class:"w-6 h-6 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"})])],-1)),e("div",null,[o[1]||(o[1]=e("h3",{class:"text-xl font-bold text-gray-900 dark:text-dark-text"},"标题生成详情",-1)),e("p",qe,"任务ID: "+d(((p=b.task)==null?void 0:p.id)||""),1)])]),e("button",{onClick:y,class:"p-2 text-gray-400 hover:text-gray-600 dark:text-dark-text-secondary dark:hover:text-dark-text rounded-lg hover:bg-gray-100 dark:hover:bg-dark-card transition-all duration-200"},o[3]||(o[3]=[e("svg",{class:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M6 18L18 6M6 6l12 12"})],-1)]))])]}),footer:l(()=>[e("div",Ct,[e("div",$t," 共 "+d(M.value)+" 条生成结果 ",1),e("div",{class:"flex items-center space-x-3"},[e("button",{onClick:y,class:"px-6 py-2.5 text-gray-700 dark:text-dark-text font-medium rounded-lg border border-gray-300 dark:border-dark-border hover:bg-gray-50 dark:hover:bg-dark-border transition-all duration-200"}," 关闭 "),e("button",{onClick:H,class:"inline-flex items-center px-6 py-2.5 bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 text-white font-medium rounded-lg shadow-lg hover:shadow-xl transition-all duration-200 transform hover:scale-105"},o[17]||(o[17]=[e("svg",{class:"w-5 h-5 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4"})],-1),$(" 导出全部 ")]))])])]),default:l(()=>[b.task?(k(),h("div",Je,[e("div",Qe,[e("div",Xe,[o[5]||(o[5]=e("div",{class:"w-8 h-8 bg-blue-500 rounded-lg flex items-center justify-center"},[e("svg",{class:"w-4 h-4 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"})])],-1)),e("div",null,[o[4]||(o[4]=e("p",{class:"text-xs text-blue-600 dark:text-blue-400 font-medium"},"任务状态",-1)),e("p",Ye,d(u(b.task.status)),1)])])]),e("div",Ze,[e("div",et,[o[7]||(o[7]=e("div",{class:"w-8 h-8 bg-green-500 rounded-lg flex items-center justify-center"},[e("svg",{class:"w-4 h-4 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"})])],-1)),e("div",null,[o[6]||(o[6]=e("p",{class:"text-xs text-green-600 dark:text-green-400 font-medium"},"图片数量",-1)),e("p",tt,d(b.task.imageCount),1)])])]),e("div",rt,[e("div",st,[o[9]||(o[9]=e("div",{class:"w-8 h-8 bg-indigo-500 rounded-lg flex items-center justify-center"},[e("svg",{class:"w-4 h-4 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"})])],-1)),e("div",null,[o[8]||(o[8]=e("p",{class:"text-xs text-indigo-600 dark:text-indigo-400 font-medium"},"生成数量",-1)),e("p",ot,d(b.task.generatedCount),1)])])]),e("div",at,[e("div",lt,[o[11]||(o[11]=e("div",{class:"w-8 h-8 bg-cyan-500 rounded-lg flex items-center justify-center"},[e("svg",{class:"w-4 h-4 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M5 3v4M3 5h4M6 17v4m-2-2h4m5-16l2.286 6.857L21 12l-5.714 2.143L13 21l-2.286-6.857L5 12l5.714-2.143L13 3z"})])],-1)),e("div",null,[o[10]||(o[10]=e("p",{class:"text-xs text-cyan-600 dark:text-cyan-400 font-medium"},"使用预设",-1)),e("p",dt,d(b.task.preset||"自定义规则"),1)])])])])):K("",!0),e("div",nt,[e("div",it,[o[12]||(o[12]=e("h4",{class:"text-sm font-medium text-gray-900 dark:text-dark-text mb-2"},"生成规则",-1)),e("p",ut,d(L.value),1)])]),e("div",ct,[o[16]||(o[16]=e("h3",{class:"text-lg font-semibold text-gray-900 dark:text-dark-text mb-4"},"标题生成结果",-1)),e("div",mt,[ee((k(),O(I,{data:j.value,style:{width:"100%"},"max-height":"400",class:"modern-table"},{default:l(()=>[s(B,{prop:"index",label:"序号",width:"80",align:"center"}),s(B,{label:"图片",width:"100",align:"center"},{default:l(p=>[p.row.status==="success"?(k(),h("div",gt,[s(G,{src:p.row.image,"preview-src-list":[p.row.image],fit:"cover",class:"w-16 h-16 rounded-lg border border-gray-200 dark:border-dark-border","preview-teleported":!0},null,8,["src","preview-src-list"])])):(k(),h("div",xt,o[13]||(o[13]=[e("div",{class:"w-16 h-16 bg-gray-100 dark:bg-dark-card rounded-lg flex items-center justify-center"},[e("svg",{class:"w-6 h-6 text-gray-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"})])],-1)])))]),_:1}),s(B,{prop:"fileName",label:"文件名",width:"150",align:"center"},{default:l(p=>[e("span",pt,d(p.row.fileName),1)]),_:1}),s(B,{prop:"title",label:"生成标题","min-width":"300"},{default:l(p=>[p.row.status==="success"?(k(),h("div",kt,[(k(!0),h(A,null,X(p.row.titles,(g,r)=>(k(),h("div",{key:r,class:"p-2 bg-gray-50 dark:bg-dark-card rounded border border-gray-200 dark:border-dark-border"},[e("div",vt,[e("span",bt,d(g),1),e("button",{onClick:T=>S(g),class:"text-blue-500 hover:text-blue-600 dark:text-blue-400 dark:hover:text-blue-300"},o[14]||(o[14]=[e("svg",{class:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M8 5H6a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2v-2M8 5a2 2 0 002 2h2a2 2 0 002-2M8 5a2 2 0 012-2h2a2 2 0 012 2m0 0h2a2 2 0 012 2v3m2 4H10m0 0l3-3m-3 3l3 3"})],-1)]),8,ht)])]))),128))])):(k(),h("div",ft,d(a(p.row.status)),1))]),_:1}),s(B,{prop:"status",label:"状态",width:"100",align:"center"},{default:l(p=>[s(U,{type:p.row.status==="success"?"success":"danger",size:"small"},{default:l(()=>[$(d(a(p.row.status)),1)]),_:2},1032,["type"])]),_:1}),s(B,{label:"操作",width:"120",align:"center"},{default:l(p=>[p.row.status==="success"?(k(),h("div",wt,[e("button",{onClick:g=>v(p.row),class:"inline-flex items-center px-3 py-1.5 text-sm font-medium text-blue-600 dark:text-blue-400 bg-blue-50 dark:bg-blue-900/30 rounded-lg hover:bg-blue-100 dark:hover:bg-blue-900/50 transition-all duration-200"}," 导出 ",8,yt)])):(k(),h("div",_t,o[15]||(o[15]=[e("span",{class:"text-sm text-gray-400"},"-",-1)])))]),_:1})]),_:1},8,["data"])),[[F,m.value]])])])]),_:1},8,["modelValue"])}}}),jt=J(Vt,[["__scopeId","data-v-752f1d1d"]]),Tt={class:"space-y-6"},Mt={class:"grid grid-cols-1 md:grid-cols-4 gap-6"},St={class:"bg-white dark:bg-dark-surface rounded-xl p-6 shadow-elegant dark:shadow-elegant-dark border border-gray-100 dark:border-dark-border hover:shadow-lg dark:hover:shadow-2xl transition-all duration-300"},Bt={class:"flex items-center justify-between"},zt={class:"text-2xl font-bold text-gray-900 dark:text-dark-text"},Dt={class:"bg-white dark:bg-dark-surface rounded-xl p-6 shadow-elegant dark:shadow-elegant-dark border border-gray-100 dark:border-dark-border hover:shadow-lg dark:hover:shadow-2xl transition-all duration-300"},Lt={class:"flex items-center justify-between"},Pt={class:"text-2xl font-bold text-green-600 dark:text-green-400"},Ht={class:"bg-white dark:bg-dark-surface rounded-xl p-6 shadow-elegant dark:shadow-elegant-dark border border-gray-100 dark:border-dark-border hover:shadow-lg dark:hover:shadow-2xl transition-all duration-300"},Gt={class:"flex items-center justify-between"},Ut={class:"text-2xl font-bold text-blue-600 dark:text-blue-400"},It={class:"bg-white dark:bg-dark-surface rounded-xl p-6 shadow-elegant dark:shadow-elegant-dark border border-gray-100 dark:border-dark-border hover:shadow-lg dark:hover:shadow-2xl transition-all duration-300"},Nt={class:"flex items-center justify-between"},Ft={class:"text-2xl font-bold text-orange-600 dark:text-orange-400"},Rt={class:"bg-white dark:bg-dark-surface rounded-xl p-6 shadow-elegant dark:shadow-elegant-dark border border-gray-100 dark:border-dark-border"},Wt={class:"flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-4 sm:space-y-0"},At={class:"flex items-center space-x-3"},Et={key:0,class:"flex items-center space-x-2 ml-4 pl-4 border-l border-gray-200 dark:border-dark-border"},Ot={class:"text-sm text-gray-600 dark:text-dark-text-secondary"},Kt={class:"bg-white dark:bg-dark-surface rounded-xl shadow-elegant dark:shadow-elegant-dark border border-gray-100 dark:border-dark-border overflow-hidden"},qt={class:"overflow-x-auto"},Jt={class:"font-mono text-sm text-gray-600 dark:text-dark-text-secondary"},Qt={class:"flex items-center space-x-2"},Xt={class:"font-medium text-gray-900 dark:text-dark-text"},Yt={class:"font-medium text-blue-600 dark:text-blue-400"},Zt={class:"text-sm text-gray-600 dark:text-dark-text-secondary"},er={class:"flex items-center space-x-2"},tr={class:"w-6 h-6 bg-gradient-to-br from-blue-400 to-blue-500 rounded-full flex items-center justify-center"},rr={class:"text-white text-xs font-medium"},sr={class:"text-sm text-gray-900 dark:text-dark-text"},or={class:"text-sm text-gray-600 dark:text-dark-text-secondary"},ar={class:"flex items-center space-x-2"},lr=["onClick"],dr={class:"flex justify-between items-center px-6 py-4 border-t border-gray-100 dark:border-dark-border bg-gray-50 dark:bg-dark-card/50"},nr={class:"text-sm text-gray-600 dark:text-dark-text-secondary"},ir=q({__name:"index",setup(P){const z=x(!1),D=x(!1),V=x(!1),C=x(null),m=x([]),M=x(2856),L=x(96.8),j=x(234),u=x(12),a=x({currentPage:1,pageSize:20,total:0}),y=x([{id:"TG001",imageCount:25,generatedCount:75,status:"completed",preset:"亚马逊标题",operator:"张三",createTime:"2024-01-15 14:30:25"},{id:"TG002",imageCount:18,generatedCount:54,status:"processing",preset:"Temu标题",operator:"李四",createTime:"2024-01-15 13:45:12"},{id:"TG003",imageCount:32,generatedCount:96,status:"completed",preset:"Shein标题",operator:"王五",createTime:"2024-01-15 12:20:08"},{id:"TG004",imageCount:15,generatedCount:0,status:"failed",preset:"自定义规则",operator:"赵六",createTime:"2024-01-15 11:15:33"},{id:"TG005",imageCount:28,generatedCount:0,status:"pending",preset:"亚马逊标题",operator:"钱七",createTime:"2024-01-15 10:30:45"},{id:"TG006",imageCount:22,generatedCount:66,status:"completed",preset:"eBay标题",operator:"孙八",createTime:"2024-01-15 09:45:22"},{id:"TG007",imageCount:19,generatedCount:38,status:"processing",preset:"自定义规则",operator:"周九",createTime:"2024-01-15 08:20:15"},{id:"TG008",imageCount:35,generatedCount:105,status:"completed",preset:"亚马逊标题",operator:"吴十",createTime:"2024-01-14 16:30:40"},{id:"TG009",imageCount:12,generatedCount:36,status:"completed",preset:"Temu标题",operator:"郑一",createTime:"2024-01-14 15:15:28"},{id:"TG010",imageCount:26,generatedCount:52,status:"processing",preset:"Shein标题",operator:"王二",createTime:"2024-01-14 14:45:55"}]),S=E(()=>{const n=(a.value.currentPage-1)*a.value.pageSize,t=n+a.value.pageSize;return y.value.slice(n,t)});ae(()=>{v()});const v=()=>{z.value=!0,setTimeout(()=>{a.value.total=y.value.length,z.value=!1},500)},H=n=>{const t={completed:"bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-300",processing:"bg-yellow-100 dark:bg-yellow-900/30 text-yellow-800 dark:text-yellow-300",failed:"bg-red-100 dark:bg-red-900/30 text-red-800 dark:text-red-300",pending:"bg-gray-100 dark:bg-gray-900/30 text-gray-800 dark:text-gray-300"};return t[n]||t.pending},b=n=>({completed:"已完成",processing:"处理中",failed:"失败",pending:"等待中"})[n]||"未知",o=n=>{m.value=n},B=n=>{C.value=n,V.value=!0},G=n=>{const{action:t,row:_}=n;switch(t){case"batchListing":F(_);break;case"smartCrop":p(_);break;case"oneClickCutout":g(_);break;case"superSplit":r(_);break}},U=()=>{f.success("导出表格功能开发中...")},I=()=>{f.success(`正在批量导出 ${m.value.length} 个任务...`)},N=()=>{f.success("标题生成任务创建成功！"),v()},F=n=>{f.success(`正在为标题生成任务 ${n.id} 创建批量刊登任务...`)},p=n=>{f.success(`正在为标题生成任务 ${n.id} 创建智能裁图任务...`)},g=n=>{f.success(`正在为标题生成任务 ${n.id} 创建一键抠图任务...`)},r=n=>{f.success(`正在为标题生成任务 ${n.id} 创建超级裂变任务...`)},T=n=>{a.value.pageSize=n,a.value.currentPage=1,v()},R=n=>{a.value.currentPage=n,v()};return(n,t)=>{const _=w("el-table-column"),i=w("el-dropdown-item"),W=w("el-dropdown-menu"),Y=w("el-dropdown"),re=w("el-table"),se=w("el-pagination"),oe=te("loading");return k(),h(A,null,[e("div",Tt,[t[25]||(t[25]=le('<div class="bg-gradient-to-r from-blue-50 to-cyan-50 dark:from-blue-900/20 dark:to-cyan-900/20 rounded-2xl p-6 border border-blue-100 dark:border-blue-800" data-v-433a9d7b><div class="flex items-center space-x-3" data-v-433a9d7b><div class="w-10 h-10 bg-gradient-to-br from-blue-500 to-blue-600 rounded-xl flex items-center justify-center" data-v-433a9d7b><svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24" data-v-433a9d7b><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" data-v-433a9d7b></path></svg></div><div data-v-433a9d7b><h1 class="text-2xl font-bold text-gray-900 dark:text-dark-text" data-v-433a9d7b>标题生成</h1><p class="mt-1 text-sm text-gray-600 dark:text-dark-text-secondary" data-v-433a9d7b>AI智能生成吸引人的商品标题</p></div></div></div>',1)),e("div",Mt,[e("div",St,[e("div",Bt,[e("div",null,[t[5]||(t[5]=e("p",{class:"text-sm font-medium text-gray-600 dark:text-dark-text-secondary"},"总生成数",-1)),e("p",zt,d(M.value),1)]),t[6]||(t[6]=e("div",{class:"w-12 h-12 bg-blue-100 dark:bg-blue-900/30 rounded-lg flex items-center justify-center"},[e("svg",{class:"w-6 h-6 text-blue-600 dark:text-blue-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"})])],-1))])]),e("div",Dt,[e("div",Lt,[e("div",null,[t[7]||(t[7]=e("p",{class:"text-sm font-medium text-gray-600 dark:text-dark-text-secondary"},"成功率",-1)),e("p",Pt,d(L.value)+"%",1)]),t[8]||(t[8]=e("div",{class:"w-12 h-12 bg-green-100 dark:bg-green-900/30 rounded-lg flex items-center justify-center"},[e("svg",{class:"w-6 h-6 text-green-600 dark:text-green-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"})])],-1))])]),e("div",Ht,[e("div",Gt,[e("div",null,[t[9]||(t[9]=e("p",{class:"text-sm font-medium text-gray-600 dark:text-dark-text-secondary"},"今日生成",-1)),e("p",Ut,d(j.value),1)]),t[10]||(t[10]=e("div",{class:"w-12 h-12 bg-blue-100 dark:bg-blue-900/30 rounded-lg flex items-center justify-center"},[e("svg",{class:"w-6 h-6 text-blue-600 dark:text-blue-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"})])],-1))])]),e("div",It,[e("div",Nt,[e("div",null,[t[11]||(t[11]=e("p",{class:"text-sm font-medium text-gray-600 dark:text-dark-text-secondary"},"处理中",-1)),e("p",Ft,d(u.value),1)]),t[12]||(t[12]=e("div",{class:"w-12 h-12 bg-orange-100 dark:bg-orange-900/30 rounded-lg flex items-center justify-center"},[e("svg",{class:"w-6 h-6 text-orange-600 dark:text-orange-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"})])],-1))])])]),e("div",Rt,[e("div",Wt,[e("div",At,[e("button",{onClick:t[0]||(t[0]=c=>D.value=!0),class:"inline-flex items-center px-4 py-2.5 bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 text-white font-medium rounded-lg shadow-lg hover:shadow-xl transition-all duration-200 transform hover:scale-105"},[s(Q(de),{class:"w-5 h-5 mr-2"}),t[13]||(t[13]=$(" 新建生成 "))]),e("button",{onClick:U,class:"inline-flex items-center px-4 py-2.5 bg-white dark:bg-dark-card text-gray-700 dark:text-dark-text font-medium rounded-lg border border-gray-300 dark:border-dark-border hover:bg-gray-50 dark:hover:bg-dark-border shadow-sm hover:shadow-md transition-all duration-200"},[s(Q(Z),{class:"w-5 h-5 mr-2"}),t[14]||(t[14]=$(" 导出表格 "))]),m.value.length>0?(k(),h("div",Et,[e("span",Ot," 已选择 "+d(m.value.length)+" 项 ",1),e("button",{onClick:I,class:"inline-flex items-center px-3 py-1.5 bg-blue-500 hover:bg-blue-600 text-white text-sm font-medium rounded-lg transition-all duration-200"},[s(Q(Z),{class:"w-4 h-4 mr-1"}),t[15]||(t[15]=$(" 批量导出 "))])])):K("",!0)])])]),e("div",Kt,[t[24]||(t[24]=e("div",{class:"px-6 py-4 border-b border-gray-100 dark:border-dark-border"},[e("h3",{class:"text-lg font-semibold text-gray-900 dark:text-dark-text"},"标题生成任务列表"),e("p",{class:"text-sm text-gray-600 dark:text-dark-text-secondary mt-1"},"管理和监控您的所有标题生成任务")],-1)),e("div",qt,[ee((k(),O(re,{data:S.value,style:{width:"100%"},onSelectionChange:o,class:"modern-table","header-cell-style":{backgroundColor:"var(--el-bg-color-page)",color:"var(--el-text-color-primary)",fontWeight:"600",borderBottom:"1px solid var(--el-border-color-light)"},"row-style":{backgroundColor:"transparent"}},{default:l(()=>[s(_,{type:"selection",width:"55"}),s(_,{prop:"id",label:"标题ID",width:"120"},{default:l(c=>[e("span",Jt,d(c.row.id),1)]),_:1}),s(_,{label:"生成数量",width:"150"},{default:l(c=>[e("div",Qt,[t[16]||(t[16]=e("span",{class:"text-sm text-gray-600 dark:text-dark-text-secondary"},"图片:",-1)),e("span",Xt,d(c.row.imageCount),1),t[17]||(t[17]=e("span",{class:"text-gray-400"},"|",-1)),t[18]||(t[18]=e("span",{class:"text-sm text-gray-600 dark:text-dark-text-secondary"},"标题:",-1)),e("span",Yt,d(c.row.generatedCount),1)])]),_:1}),s(_,{prop:"status",label:"生成状态",width:"120"},{default:l(c=>[e("span",{class:ne([H(c.row.status),"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium"])},d(b(c.row.status)),3)]),_:1}),s(_,{prop:"preset",label:"使用预设",width:"150"},{default:l(c=>[e("span",Zt,d(c.row.preset||"自定义规则"),1)]),_:1}),s(_,{prop:"operator",label:"操作人",width:"100"},{default:l(c=>[e("div",er,[e("div",tr,[e("span",rr,d(c.row.operator.charAt(0)),1)]),e("span",sr,d(c.row.operator),1)])]),_:1}),s(_,{prop:"createTime",label:"创建时间",width:"180"},{default:l(c=>[e("div",or,d(c.row.createTime),1)]),_:1}),s(_,{label:"操作",width:"180"},{default:l(c=>[e("div",ar,[e("button",{onClick:ur=>B(c.row),class:"inline-flex items-center px-3 py-1.5 text-sm font-medium text-blue-600 dark:text-blue-400 hover:text-blue-700 dark:hover:text-blue-300 bg-blue-50 dark:bg-blue-900/20 hover:bg-blue-100 dark:hover:bg-blue-900/30 rounded-lg transition-all duration-200"}," 查看详情 ",8,lr),s(Y,{onCommand:G,trigger:"click"},{dropdown:l(()=>[s(W,null,{default:l(()=>[s(i,{command:{action:"batchListing",row:c.row}},{default:l(()=>t[19]||(t[19]=[e("div",{class:"flex items-center space-x-2"},[e("svg",{class:"w-4 h-4 text-green-500",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"})]),e("span",null,"批量刊登")],-1)])),_:2,__:[19]},1032,["command"]),s(i,{command:{action:"smartCrop",row:c.row}},{default:l(()=>t[20]||(t[20]=[e("div",{class:"flex items-center space-x-2"},[e("svg",{class:"w-4 h-4 text-yellow-500",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"})]),e("span",null,"智能裁图")],-1)])),_:2,__:[20]},1032,["command"]),s(i,{command:{action:"oneClickCutout",row:c.row}},{default:l(()=>t[21]||(t[21]=[e("div",{class:"flex items-center space-x-2"},[e("svg",{class:"w-4 h-4 text-pink-500",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M7 4V2a1 1 0 011-1h8a1 1 0 011 1v2m-9 0h10m-10 0a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V6a2 2 0 00-2-2"})]),e("span",null,"一键抠图")],-1)])),_:2,__:[21]},1032,["command"]),s(i,{command:{action:"superSplit",row:c.row}},{default:l(()=>t[22]||(t[22]=[e("div",{class:"flex items-center space-x-2"},[e("svg",{class:"w-4 h-4 text-purple-500",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M13 10V3L4 14h7v7l9-11h-7z"})]),e("span",null,"超级裂变")],-1)])),_:2,__:[22]},1032,["command"])]),_:2},1024)]),default:l(()=>[t[23]||(t[23]=e("button",{class:"inline-flex items-center px-2 py-1.5 text-sm font-medium text-gray-600 dark:text-dark-text-secondary hover:text-gray-700 dark:hover:text-dark-text bg-gray-50 dark:bg-dark-card hover:bg-gray-100 dark:hover:bg-dark-border rounded-lg transition-all duration-200"},[$(" 更多 "),e("svg",{class:"w-4 h-4 ml-1",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M19 9l-7 7-7-7"})])],-1))]),_:2,__:[23]},1024)])]),_:1})]),_:1},8,["data"])),[[oe,z.value]])]),e("div",dr,[e("div",nr," 共 "+d(a.value.total)+" 条记录 ",1),s(se,{"current-page":a.value.currentPage,"onUpdate:currentPage":t[1]||(t[1]=c=>a.value.currentPage=c),"page-size":a.value.pageSize,"onUpdate:pageSize":t[2]||(t[2]=c=>a.value.pageSize=c),"page-sizes":[10,20,50,100],total:a.value.total,layout:"sizes, prev, pager, next, jumper",onSizeChange:T,onCurrentChange:R,class:"modern-pagination"},null,8,["current-page","page-size","total"])])])]),s(Ee,{modelValue:D.value,"onUpdate:modelValue":t[3]||(t[3]=c=>D.value=c),onSuccess:N},null,8,["modelValue"]),s(jt,{modelValue:V.value,"onUpdate:modelValue":t[4]||(t[4]=c=>V.value=c),task:C.value},null,8,["modelValue","task"])],64)}}}),gr=J(ir,[["__scopeId","data-v-433a9d7b"]]);export{gr as default};
